#!/usr/bin/env python3
"""
连珠大师 - 五子棋游戏
主程序入口文件

使用方法:
    python main.py

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.main_window import create_and_run_game
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有必要的模块都已正确安装。")
    sys.exit(1)

def main():
    """主函数"""
    try:
        print("正在启动连珠大师...")
        print("游戏规则：")
        print("- 黑棋先手")
        print("- 连成五子获胜")
        print("- 点击棋盘交叉点落子")
        print("- 支持悔棋功能")
        print("-" * 30)
        
        # 创建并运行游戏
        create_and_run_game()
        
    except KeyboardInterrupt:
        print("\n游戏被用户中断。")
    except Exception as e:
        print(f"游戏运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("感谢游玩连珠大师！")

if __name__ == "__main__":
    main()
