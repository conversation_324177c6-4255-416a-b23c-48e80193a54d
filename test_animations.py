#!/usr/bin/env python3
"""
动画功能测试程序
用于验证落子动画和其他动画效果是否正常工作
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.game_board_ui import GameBoardUI
    from game.player import PieceColor
    from config.settings import CURRENT_THEME, ENABLE_ANIMATIONS
    print(f"动画功能状态: {'启用' if ENABLE_ANIMATIONS else '禁用'}")
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class AnimationTestWindow:
    """动画测试窗口"""
    
    def __init__(self):
        """初始化测试窗口"""
        self.root = tk.Tk()
        self.root.title("五子棋动画测试")
        self.root.geometry("800x700")
        self.root.configure(bg=CURRENT_THEME['interface_bg'])
        
        # 创建主框架
        main_frame = tk.Frame(self.root, bg=CURRENT_THEME['interface_bg'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="五子棋动画功能测试",
            font=('Microsoft YaHei', 16, 'bold'),
            bg=CURRENT_THEME['interface_bg'],
            fg=CURRENT_THEME['text_color']
        )
        title_label.pack(pady=(0, 20))
        
        # 创建棋盘区域
        board_frame = tk.Frame(main_frame, bg=CURRENT_THEME['interface_bg'])
        board_frame.pack(side='left', fill='both', expand=True)
        
        # 创建棋盘UI
        self.board_ui = GameBoardUI(board_frame, self._on_board_click)
        self.board_ui.get_widget().pack()
        
        # 创建控制面板
        control_frame = tk.Frame(main_frame, bg=CURRENT_THEME['interface_bg'], width=200)
        control_frame.pack(side='right', fill='y', padx=(20, 0))
        control_frame.pack_propagate(False)
        
        self._create_controls(control_frame)
        
        # 游戏状态
        self.current_player = PieceColor.BLACK
        self.move_count = 0
        
        # 更新状态显示
        self._update_status()
    
    def _create_controls(self, parent):
        """创建控制按钮"""
        # 状态显示
        self.status_label = tk.Label(
            parent,
            text="当前玩家: 黑棋",
            font=('Microsoft YaHei', 12, 'bold'),
            bg=CURRENT_THEME['interface_bg'],
            fg=CURRENT_THEME['accent_color']
        )
        self.status_label.pack(pady=(0, 20))
        
        # 步数显示
        self.move_label = tk.Label(
            parent,
            text="步数: 0",
            font=('Microsoft YaHei', 10),
            bg=CURRENT_THEME['interface_bg'],
            fg=CURRENT_THEME['text_color']
        )
        self.move_label.pack(pady=(0, 20))
        
        # 测试按钮
        test_frame = tk.LabelFrame(
            parent,
            text="动画测试",
            font=('Microsoft YaHei', 10, 'bold'),
            bg=CURRENT_THEME['interface_bg'],
            fg=CURRENT_THEME['text_color']
        )
        test_frame.pack(fill='x', pady=(0, 20))
        
        # 自动落子测试
        auto_button = tk.Button(
            test_frame,
            text="自动落子测试",
            command=self._auto_place_test,
            bg=CURRENT_THEME['accent_color'],
            fg='white',
            font=('Microsoft YaHei', 9),
            relief='flat',
            padx=10,
            pady=5
        )
        auto_button.pack(fill='x', padx=5, pady=5)
        
        # 获胜连线测试
        win_button = tk.Button(
            test_frame,
            text="获胜连线测试",
            command=self._win_line_test,
            bg='#4CAF50',
            fg='white',
            font=('Microsoft YaHei', 9),
            relief='flat',
            padx=10,
            pady=5
        )
        win_button.pack(fill='x', padx=5, pady=5)
        
        # 庆祝动画测试
        celebration_button = tk.Button(
            test_frame,
            text="庆祝动画测试",
            command=self._celebration_test,
            bg='#FF9800',
            fg='white',
            font=('Microsoft YaHei', 9),
            relief='flat',
            padx=10,
            pady=5
        )
        celebration_button.pack(fill='x', padx=5, pady=5)
        
        # 清空棋盘
        clear_button = tk.Button(
            test_frame,
            text="清空棋盘",
            command=self._clear_board,
            bg='#F44336',
            fg='white',
            font=('Microsoft YaHei', 9),
            relief='flat',
            padx=10,
            pady=5
        )
        clear_button.pack(fill='x', padx=5, pady=5)
        
        # 说明文本
        info_label = tk.Label(
            parent,
            text="点击棋盘进行落子\n观察动画效果\n\n使用测试按钮验证\n各种动画功能",
            font=('Microsoft YaHei', 9),
            bg=CURRENT_THEME['interface_bg'],
            fg='#666666',
            justify='left'
        )
        info_label.pack(pady=(20, 0))
    
    def _on_board_click(self, row: int, col: int):
        """处理棋盘点击"""
        if self.board_ui.board_state[row][col] != PieceColor.EMPTY:
            return
        
        # 放置棋子（使用动画）
        self.board_ui.place_piece(row, col, self.current_player, animated=True)
        
        # 切换玩家
        self.current_player = PieceColor.WHITE if self.current_player == PieceColor.BLACK else PieceColor.BLACK
        self.move_count += 1
        
        # 更新状态
        self._update_status()
        
        print(f"玩家点击位置 ({row}, {col})，当前步数: {self.move_count}")
    
    def _auto_place_test(self):
        """自动落子测试"""
        import random
        
        # 随机选择5个空位置进行落子
        empty_positions = []
        for row in range(15):
            for col in range(15):
                if self.board_ui.board_state[row][col] == PieceColor.EMPTY:
                    empty_positions.append((row, col))
        
        if len(empty_positions) < 5:
            messagebox.showinfo("提示", "棋盘空位不足，请先清空棋盘")
            return
        
        # 随机选择5个位置
        test_positions = random.sample(empty_positions, 5)
        
        def place_next(index):
            if index >= len(test_positions):
                return
            
            row, col = test_positions[index]
            color = PieceColor.BLACK if index % 2 == 0 else PieceColor.WHITE
            
            self.board_ui.place_piece(row, col, color, animated=True)
            self.move_count += 1
            
            # 延迟放置下一个棋子
            self.root.after(800, lambda: place_next(index + 1))
        
        place_next(0)
        print("开始自动落子测试...")
    
    def _win_line_test(self):
        """获胜连线测试"""
        # 创建一条测试连线（对角线）
        win_line = [(5, 5), (6, 6), (7, 7), (8, 8), (9, 9)]
        
        # 先放置棋子
        for row, col in win_line:
            self.board_ui.place_piece(row, col, PieceColor.BLACK, animated=False)
        
        # 延迟绘制获胜连线
        self.root.after(500, lambda: self.board_ui.draw_win_line(win_line, animated=True))
        print("开始获胜连线测试...")
    
    def _celebration_test(self):
        """庆祝动画测试"""
        # 在棋盘中心播放庆祝动画
        center_x = self.board_ui.canvas_size // 2
        center_y = self.board_ui.canvas_size // 2
        
        self.board_ui.animation_manager.animate_celebration(center_x, center_y)
        print("开始庆祝动画测试...")
    
    def _clear_board(self):
        """清空棋盘"""
        self.board_ui.clear_board()
        self.current_player = PieceColor.BLACK
        self.move_count = 0
        self._update_status()
        print("棋盘已清空")
    
    def _update_status(self):
        """更新状态显示"""
        player_name = "黑棋" if self.current_player == PieceColor.BLACK else "白棋"
        self.status_label.config(text=f"当前玩家: {player_name}")
        self.move_label.config(text=f"步数: {self.move_count}")
        
        # 更新棋盘当前玩家
        self.board_ui.set_current_player(self.current_player)
    
    def run(self):
        """运行测试窗口"""
        print("动画测试窗口已启动")
        print("- 点击棋盘进行落子测试")
        print("- 使用右侧按钮测试各种动画效果")
        self.root.mainloop()

def main():
    """主函数"""
    try:
        print("启动五子棋动画测试程序...")
        app = AnimationTestWindow()
        app.run()
    except Exception as e:
        print(f"测试程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
