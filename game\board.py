"""
棋盘逻辑模块
管理棋盘状态、落子验证等核心功能
"""

from typing import List, Tuple, Optional
from .player import PieceColor
from config.settings import BOARD_SIZE

class Move:
    """落子记录类"""
    
    def __init__(self, row: int, col: int, color: PieceColor):
        """
        初始化落子记录
        
        Args:
            row: 行坐标
            col: 列坐标
            color: 棋子颜色
        """
        self.row = row
        self.col = col
        self.color = color
    
    def __str__(self):
        return f"({self.row}, {self.col}) - {self.color.name}"

class GameBoard:
    """游戏棋盘类"""
    
    def __init__(self, size: int = BOARD_SIZE):
        """
        初始化棋盘
        
        Args:
            size: 棋盘大小，默认15x15
        """
        self.size = size
        self.board = [[PieceColor.EMPTY for _ in range(size)] for _ in range(size)]
        self.move_history: List[Move] = []  # 落子历史
        self.last_move: Optional[Move] = None  # 最后一步落子
    
    def is_valid_position(self, row: int, col: int) -> bool:
        """
        检查位置是否有效
        
        Args:
            row: 行坐标
            col: 列坐标
            
        Returns:
            bool: 位置是否有效
        """
        return 0 <= row < self.size and 0 <= col < self.size
    
    def is_empty(self, row: int, col: int) -> bool:
        """
        检查位置是否为空
        
        Args:
            row: 行坐标
            col: 列坐标
            
        Returns:
            bool: 位置是否为空
        """
        if not self.is_valid_position(row, col):
            return False
        return self.board[row][col] == PieceColor.EMPTY
    
    def place_piece(self, row: int, col: int, color: PieceColor) -> bool:
        """
        在指定位置放置棋子
        
        Args:
            row: 行坐标
            col: 列坐标
            color: 棋子颜色
            
        Returns:
            bool: 是否成功放置
        """
        if not self.is_empty(row, col):
            return False
        
        self.board[row][col] = color
        move = Move(row, col, color)
        self.move_history.append(move)
        self.last_move = move
        return True
    
    def undo_last_move(self) -> bool:
        """
        悔棋（撤销最后一步）
        
        Returns:
            bool: 是否成功悔棋
        """
        if not self.move_history:
            return False
        
        last_move = self.move_history.pop()
        self.board[last_move.row][last_move.col] = PieceColor.EMPTY
        
        # 更新最后一步记录
        self.last_move = self.move_history[-1] if self.move_history else None
        return True
    
    def get_piece(self, row: int, col: int) -> PieceColor:
        """
        获取指定位置的棋子
        
        Args:
            row: 行坐标
            col: 列坐标
            
        Returns:
            PieceColor: 棋子颜色
        """
        if not self.is_valid_position(row, col):
            return PieceColor.EMPTY
        return self.board[row][col]
    
    def clear_board(self):
        """清空棋盘"""
        self.board = [[PieceColor.EMPTY for _ in range(self.size)] for _ in range(self.size)]
        self.move_history.clear()
        self.last_move = None
    
    def get_move_count(self) -> int:
        """获取已下棋子数量"""
        return len(self.move_history)
    
    def is_board_full(self) -> bool:
        """检查棋盘是否已满"""
        return self.get_move_count() >= self.size * self.size
    
    def get_star_positions(self) -> List[Tuple[int, int]]:
        """
        获取星位坐标（天元和四个角的星位）
        
        Returns:
            List[Tuple[int, int]]: 星位坐标列表
        """
        center = self.size // 2
        quarter = self.size // 4
        three_quarter = self.size - quarter - 1
        
        return [
            (center, center),  # 天元
            (quarter, quarter),  # 左上
            (quarter, three_quarter),  # 右上
            (three_quarter, quarter),  # 左下
            (three_quarter, three_quarter)  # 右下
        ]
