"""
控制面板UI组件
包含玩家信息、游戏状态、功能按钮等
"""

import tkinter as tk
from typing import Callable, Optional
from game.player import PieceColor
from ui.styles import style_manager
from config.settings import CURRENT_THEME

class ControlPanel:
    """控制面板类"""

    def __init__(self, parent: tk.Widget):
        """
        初始化控制面板

        Args:
            parent: 父组件
        """
        self.parent = parent
        self.theme = CURRENT_THEME
        self.style_manager = style_manager

        # 回调函数
        self.on_new_game: Optional[Callable] = None
        self.on_undo: Optional[Callable] = None
        self.on_settings: Optional[Callable] = None
        self.on_exit: Optional[Callable] = None

        # 创建主框架
        self.main_frame = tk.Frame(parent, **self.style_manager.get_frame_style())

        # 创建各个组件
        self._create_title()
        self._create_player_info()
        self._create_game_status()
        self._create_buttons()
        self._create_game_info()

        # 布局
        self._layout_components()

    def _create_title(self):
        """创建标题"""
        self.title_label = tk.Label(
            self.main_frame,
            text="连珠大师",
            **self.style_manager.get_label_style('title')
        )

    def _create_player_info(self):
        """创建玩家信息区域"""
        # 玩家信息框架
        self.player_frame = tk.Frame(self.main_frame, **self.style_manager.get_frame_style())

        # 黑棋玩家信息
        self.black_player_frame = tk.Frame(self.player_frame, **self.style_manager.get_frame_style())

        # 黑棋标识
        self.black_piece_canvas = tk.Canvas(
            self.black_player_frame,
            width=30, height=30,
            bg=self.theme['interface_bg'],
            highlightthickness=0
        )
        self._draw_piece_indicator(self.black_piece_canvas, PieceColor.BLACK)

        self.black_player_label = tk.Label(
            self.black_player_frame,
            text="玩家1 (黑棋)",
            **self.style_manager.get_label_style('normal')
        )

        # 白棋玩家信息
        self.white_player_frame = tk.Frame(self.player_frame, **self.style_manager.get_frame_style())

        # 白棋标识
        self.white_piece_canvas = tk.Canvas(
            self.white_player_frame,
            width=30, height=30,
            bg=self.theme['interface_bg'],
            highlightthickness=0
        )
        self._draw_piece_indicator(self.white_piece_canvas, PieceColor.WHITE)

        self.white_player_label = tk.Label(
            self.white_player_frame,
            text="玩家2 (白棋)",
            **self.style_manager.get_label_style('normal')
        )

        # 当前回合指示器
        self.current_turn_frame = tk.Frame(self.player_frame, **self.style_manager.get_frame_style())
        self.current_turn_label = tk.Label(
            self.current_turn_frame,
            text="黑棋先手",
            **self.style_manager.get_label_style('status')
        )

    def _draw_piece_indicator(self, canvas: tk.Canvas, color: PieceColor):
        """
        在画布上绘制棋子指示器

        Args:
            canvas: 画布
            color: 棋子颜色
        """
        center_x, center_y = 15, 15
        radius = 10

        piece_color = self.theme['black_piece'] if color == PieceColor.BLACK else self.theme['white_piece']
        outline_color = self.theme['board_lines']

        canvas.create_oval(
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius,
            fill=piece_color,
            outline=outline_color,
            width=1
        )

    def _create_game_status(self):
        """创建游戏状态显示"""
        self.status_frame = tk.Frame(self.main_frame, **self.style_manager.get_frame_style())

        self.status_label = tk.Label(
            self.status_frame,
            text="点击开始新游戏",
            **self.style_manager.get_label_style('status')
        )

    def _create_buttons(self):
        """创建功能按钮"""
        self.button_frame = tk.Frame(self.main_frame, **self.style_manager.get_frame_style())

        # 新游戏按钮
        self.new_game_button = self.style_manager.create_rounded_button(
            self.button_frame,
            text="新游戏",
            command=self._on_new_game_click,
            button_type='primary'
        )

        # 悔棋按钮
        self.undo_button = self.style_manager.create_rounded_button(
            self.button_frame,
            text="悔棋",
            command=self._on_undo_click,
            button_type='normal'
        )

        # 设置按钮
        self.settings_button = self.style_manager.create_rounded_button(
            self.button_frame,
            text="设置",
            command=self._on_settings_click,
            button_type='normal'
        )

        # 退出按钮
        self.exit_button = self.style_manager.create_rounded_button(
            self.button_frame,
            text="退出",
            command=self._on_exit_click,
            button_type='danger'
        )

        # 初始状态下禁用悔棋按钮
        self.undo_button.config(state='disabled')

    def _create_game_info(self):
        """创建游戏信息显示"""
        self.info_frame = tk.Frame(self.main_frame, **self.style_manager.get_frame_style())

        # 步数显示
        self.move_count_label = tk.Label(
            self.info_frame,
            text="步数: 0",
            **self.style_manager.get_label_style('normal')
        )

        # 游戏规则提示
        self.rule_label = tk.Label(
            self.info_frame,
            text="连成五子获胜",
            **self.style_manager.get_label_style('normal')
        )
        self.rule_label.config(fg='#666666', font=(self.style_manager.fonts['normal'][0], 10))

    def _layout_components(self):
        """布局所有组件"""
        # 主框架布局
        self.main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # 标题
        self.title_label.pack(pady=(0, 20))

        # 玩家信息区域
        self.player_frame.pack(fill='x', pady=(0, 20))

        # 黑棋玩家
        self.black_player_frame.pack(fill='x', pady=(0, 10))
        self.black_piece_canvas.pack(side='left', padx=(0, 10))
        self.black_player_label.pack(side='left')

        # 白棋玩家
        self.white_player_frame.pack(fill='x', pady=(0, 10))
        self.white_piece_canvas.pack(side='left', padx=(0, 10))
        self.white_player_label.pack(side='left')

        # 当前回合
        self.current_turn_frame.pack(fill='x', pady=(10, 0))
        self.current_turn_label.pack()

        # 游戏状态
        self.status_frame.pack(fill='x', pady=(20, 20))
        self.status_label.pack()

        # 按钮区域
        self.button_frame.pack(fill='x', pady=(0, 20))
        self.new_game_button.pack(fill='x', pady=(0, 5))
        self.undo_button.pack(fill='x', pady=(0, 5))
        self.settings_button.pack(fill='x', pady=(0, 5))
        self.exit_button.pack(fill='x', pady=(0, 5))

        # 游戏信息
        self.info_frame.pack(fill='x')
        self.move_count_label.pack(pady=(0, 5))
        self.rule_label.pack()

    def _on_new_game_click(self):
        """新游戏按钮点击事件"""
        if self.on_new_game:
            self.on_new_game()

    def _on_undo_click(self):
        """悔棋按钮点击事件"""
        if self.on_undo:
            self.on_undo()

    def _on_settings_click(self):
        """设置按钮点击事件"""
        if self.on_settings:
            self.on_settings()

    def _on_exit_click(self):
        """退出按钮点击事件"""
        if self.on_exit:
            self.on_exit()

    def update_current_player(self, color: PieceColor):
        """
        更新当前玩家显示

        Args:
            color: 当前玩家棋子颜色
        """
        if color == PieceColor.BLACK:
            self.current_turn_label.config(text="黑棋回合")
            # 高亮黑棋玩家
            self.black_player_frame.config(relief='solid', borderwidth=2)
            self.white_player_frame.config(relief='flat', borderwidth=0)
        else:
            self.current_turn_label.config(text="白棋回合")
            # 高亮白棋玩家
            self.white_player_frame.config(relief='solid', borderwidth=2)
            self.black_player_frame.config(relief='flat', borderwidth=0)

    def update_status(self, status_text: str):
        """
        更新游戏状态显示

        Args:
            status_text: 状态文本
        """
        self.status_label.config(text=status_text)

    def update_move_count(self, count: int):
        """
        更新步数显示

        Args:
            count: 当前步数
        """
        self.move_count_label.config(text=f"步数: {count}")

    def set_undo_enabled(self, enabled: bool):
        """
        设置悔棋按钮是否可用

        Args:
            enabled: 是否可用
        """
        self.undo_button.config(state='normal' if enabled else 'disabled')

    def get_widget(self) -> tk.Frame:
        """获取主框架组件"""
        return self.main_frame
