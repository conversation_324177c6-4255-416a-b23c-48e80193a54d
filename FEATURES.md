# 连珠大师 - 功能特性详细说明

## 已实现功能

### 1. 棋盘大小选择功能 ✅

**功能描述**: 支持多种棋盘大小选择，满足不同玩家的需求。

**技术实现**:
- 支持的棋盘大小：11x11、13x13、15x15、17x17、19x19
- 动态棋盘创建和UI适配
- 设置保存和恢复
- 棋盘大小改变时自动重新创建UI组件

**使用方法**:
1. 点击"设置"按钮
2. 在"棋盘大小"区域选择所需大小
3. 点击"确定"应用设置
4. 如果游戏正在进行，系统会询问是否重新开始

### 2. AI对弈功能 ✅

**功能描述**: 实现了智能AI对手，提供四个难度级别的挑战。

**AI算法特性**:
- **算法**: Minimax算法 + Alpha-Beta剪枝
- **评估函数**: 多维度棋型评估（连子数、阻挡情况、位置价值）
- **性能优化**: 候选位置筛选、搜索深度限制
- **智能防守**: 优先阻止对手获胜

**四个难度级别**:

#### 简单难度
- **搜索深度**: 2层
- **特点**: 基本攻防 + 随机选择
- **适合**: 初学者和休闲玩家
- **行为**: 会阻止明显的获胜机会，但缺乏长远规划

#### 中等难度
- **搜索深度**: 3层
- **特点**: 2-3步前瞻计算
- **适合**: 有一定经验的玩家
- **行为**: 能够进行简单的战术组合

#### 困难难度
- **搜索深度**: 4层
- **特点**: 3-4步深度搜索
- **适合**: 需要仔细思考的玩家
- **行为**: 具备较强的战术意识和防守能力

#### 专家难度
- **搜索深度**: 5层
- **特点**: 4-5步深度分析
- **适合**: 高水平玩家
- **行为**: 具备强大的计算能力和战略规划

**AI技术细节**:
- **位置评估**: 考虑中心位置加分、连子潜力等因素
- **剪枝优化**: Alpha-Beta剪枝大幅提升搜索效率
- **候选筛选**: 智能筛选有价值的落子位置
- **获胜检测**: 快速检测获胜和必败局面

### 3. 游戏设置系统 ✅

**功能描述**: 完整的游戏设置界面，支持各种游戏参数配置。

**设置选项**:
- **棋盘大小**: 5种尺寸可选
- **游戏模式**: 双人对战 / 人机对战
- **AI难度**: 4个级别可选
- **设置保存**: 自动保存用户偏好

**界面特性**:
- 模态对话框设计
- 实时设置预览
- 一键恢复默认设置
- 设置验证和错误处理

### 4. 增强的用户界面 ✅

**新增UI组件**:
- **设置对话框**: 专业的设置界面
- **AI思考提示**: 显示AI计算状态
- **游戏模式指示**: 清晰显示当前模式
- **难度显示**: 实时显示AI难度

**交互改进**:
- AI回合时禁用用户输入
- 设置更改时智能提示
- 流畅的模式切换
- 优化的按钮布局

## 技术架构

### 模块化设计
```
game/
├── ai_player.py      # AI算法实现
├── game_logic.py     # 游戏逻辑扩展
└── ...

ui/
├── settings_dialog.py # 设置界面
├── main_window.py     # 主窗口增强
└── ...

config/
└── settings.py        # 配置扩展
```

### 核心算法

#### Minimax算法实现
```python
def _minimax(self, board, depth, is_maximizing, alpha, beta):
    # 递归搜索最优解
    # Alpha-Beta剪枝优化
    # 返回最佳评估分数
```

#### 评估函数
```python
def _evaluate_line(self, board, row, col, dx, dy, color):
    # 评估指定方向的连子价值
    # 考虑连子数量和阻挡情况
    # 返回该方向的分数
```

### 性能优化

1. **搜索优化**:
   - 候选位置限制（最多20个）
   - 按评分排序优先搜索
   - Alpha-Beta剪枝减少计算量

2. **UI优化**:
   - 异步AI计算
   - 进度提示
   - 响应式界面更新

3. **内存优化**:
   - 棋盘状态复用
   - 及时释放临时对象
   - 高效的数据结构

## 使用指南

### 开始人机对战
1. 启动游戏
2. 点击"设置"按钮
3. 选择"人机对战"模式
4. 选择合适的AI难度
5. 选择棋盘大小
6. 点击"确定"保存设置
7. 点击"新游戏"开始对弈

### 调整难度
- 游戏过程中可随时通过"设置"调整AI难度
- 难度更改后建议重新开始游戏以获得最佳体验

### 棋盘大小建议
- **11x11**: 快速对局，适合练习
- **13x13**: 平衡的游戏体验
- **15x15**: 标准五子棋规格
- **17x17**: 更具战略性
- **19x19**: 最大挑战，需要长远规划

## 未来扩展可能

1. **AI增强**:
   - 开局库支持
   - 残局数据库
   - 机器学习优化

2. **功能扩展**:
   - 网络对战
   - 游戏录像
   - 复盘分析

3. **界面优化**:
   - 更多主题
   - 动画效果
   - 音效支持

---

**总结**: 本项目成功实现了完整的五子棋游戏，包含多种棋盘大小选择和四个难度级别的AI对手，为玩家提供了丰富的游戏体验。
