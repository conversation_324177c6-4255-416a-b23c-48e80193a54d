"""
游戏棋盘UI组件
负责棋盘的绘制、交互和动画效果
"""

import tkinter as tk
from tkinter import Canvas
import math
from typing import Callable, Optional, Tuple, List
from game.player import PieceColor
from config.settings import BOARD_SIZE, CELL_SIZE, BOARD_MARGIN, CURRENT_THEME, ENABLE_ANIMATIONS
from ui.animations import AnimationManager

class GameBoardUI:
    """游戏棋盘UI类"""

    def __init__(self, parent: tk.Widget, on_click_callback: Callable[[int, int], None]):
        """
        初始化棋盘UI

        Args:
            parent: 父组件
            on_click_callback: 点击回调函数，参数为(row, col)
        """
        self.parent = parent
        self.on_click_callback = on_click_callback
        self.theme = CURRENT_THEME

        # 计算棋盘尺寸
        self.board_size = BOARD_SIZE
        self.cell_size = CELL_SIZE
        self.margin = BOARD_MARGIN
        self.canvas_size = (self.board_size - 1) * self.cell_size + 2 * self.margin

        # 创建画布
        self.canvas = Canvas(
            parent,
            width=self.canvas_size,
            height=self.canvas_size,
            bg=self.theme['board_bg'],
            highlightthickness=0
        )

        # 棋盘状态
        self.board_state = [[PieceColor.EMPTY for _ in range(self.board_size)] for _ in range(self.board_size)]
        self.last_move_pos: Optional[Tuple[int, int]] = None
        self.win_line: List[Tuple[int, int]] = []
        self.hover_pos: Optional[Tuple[int, int]] = None
        self.current_player_color = PieceColor.BLACK

        # 动画管理器
        self.animation_manager = AnimationManager(self.canvas)

        # 绑定事件
        self.canvas.bind('<Button-1>', self._on_canvas_click)
        self.canvas.bind('<Motion>', self._on_mouse_move)
        self.canvas.bind('<Leave>', self._on_mouse_leave)

        # 绘制棋盘
        self._draw_board()

    def _draw_board(self):
        """绘制棋盘网格和星位"""
        self.canvas.delete('board_lines')
        self.canvas.delete('star_points')

        # 绘制网格线
        for i in range(self.board_size):
            # 垂直线
            x = self.margin + i * self.cell_size
            self.canvas.create_line(
                x, self.margin,
                x, self.canvas_size - self.margin,
                fill=self.theme['board_lines'],
                width=1,
                tags='board_lines'
            )

            # 水平线
            y = self.margin + i * self.cell_size
            self.canvas.create_line(
                self.margin, y,
                self.canvas_size - self.margin, y,
                fill=self.theme['board_lines'],
                width=1,
                tags='board_lines'
            )

        # 绘制星位
        star_positions = self._get_star_positions()
        for row, col in star_positions:
            x, y = self._board_to_canvas(row, col)
            self.canvas.create_oval(
                x - 3, y - 3, x + 3, y + 3,
                fill=self.theme['board_lines'],
                outline=self.theme['board_lines'],
                tags='star_points'
            )

    def _get_star_positions(self) -> List[Tuple[int, int]]:
        """获取星位坐标"""
        center = self.board_size // 2
        quarter = 3  # 标准五子棋星位
        three_quarter = self.board_size - quarter - 1

        return [
            (center, center),  # 天元
            (quarter, quarter),  # 左上
            (quarter, three_quarter),  # 右上
            (three_quarter, quarter),  # 左下
            (three_quarter, three_quarter)  # 右下
        ]

    def _board_to_canvas(self, row: int, col: int) -> Tuple[int, int]:
        """
        将棋盘坐标转换为画布坐标

        Args:
            row: 棋盘行坐标
            col: 棋盘列坐标

        Returns:
            Tuple[int, int]: 画布坐标(x, y)
        """
        x = self.margin + col * self.cell_size
        y = self.margin + row * self.cell_size
        return x, y

    def _canvas_to_board(self, x: int, y: int) -> Optional[Tuple[int, int]]:
        """
        将画布坐标转换为棋盘坐标

        Args:
            x: 画布x坐标
            y: 画布y坐标

        Returns:
            Optional[Tuple[int, int]]: 棋盘坐标(row, col)，如果无效则返回None
        """
        # 计算最近的交叉点
        col = round((x - self.margin) / self.cell_size)
        row = round((y - self.margin) / self.cell_size)

        # 检查是否在有效范围内
        if 0 <= row < self.board_size and 0 <= col < self.board_size:
            # 检查点击是否足够接近交叉点
            canvas_x, canvas_y = self._board_to_canvas(row, col)
            distance = math.sqrt((x - canvas_x) ** 2 + (y - canvas_y) ** 2)
            if distance <= self.cell_size * 0.4:  # 允许的点击范围
                return row, col

        return None

    def _on_canvas_click(self, event):
        """处理画布点击事件"""
        pos = self._canvas_to_board(event.x, event.y)
        if pos and self.board_state[pos[0]][pos[1]] == PieceColor.EMPTY:
            self.on_click_callback(pos[0], pos[1])

    def _on_mouse_move(self, event):
        """处理鼠标移动事件"""
        pos = self._canvas_to_board(event.x, event.y)
        if pos and self.board_state[pos[0]][pos[1]] == PieceColor.EMPTY:
            if self.hover_pos != pos:
                self.hover_pos = pos
                self._update_hover_indicator()
        else:
            if self.hover_pos is not None:
                self.hover_pos = None
                self._update_hover_indicator()

    def _on_mouse_leave(self, event):
        """处理鼠标离开事件"""
        if self.hover_pos is not None:
            self.hover_pos = None
            self._update_hover_indicator()

    def _update_hover_indicator(self):
        """更新悬停指示器"""
        self.canvas.delete('hover_indicator')

        if self.hover_pos:
            row, col = self.hover_pos
            x, y = self._board_to_canvas(row, col)
            radius = self.cell_size // 3

            # 绘制更美观的悬停效果
            piece_color = self.theme['black_piece'] if self.current_player_color == PieceColor.BLACK else self.theme['white_piece']

            # 外圈光晕效果
            self.canvas.create_oval(
                x - radius - 3, y - radius - 3,
                x + radius + 3, y + radius + 3,
                fill='',
                outline=self.theme['hover_color'],
                width=2,
                tags='hover_indicator'
            )

            # 半透明棋子预览
            hover_piece = self.canvas.create_oval(
                x - radius, y - radius,
                x + radius, y + radius,
                fill=piece_color,
                outline=self.theme['hover_color'],
                width=1,
                stipple='gray50',  # 半透明效果
                tags='hover_indicator'
            )

            # 添加轻微的脉冲动画
            if ENABLE_ANIMATIONS:
                self.animation_manager.animate_hover_pulse(hover_piece, piece_color)

    def place_piece(self, row: int, col: int, color: PieceColor, animated: bool = True):
        """
        在指定位置放置棋子

        Args:
            row: 行坐标
            col: 列坐标
            color: 棋子颜色
            animated: 是否使用动画
        """
        if self.board_state[row][col] != PieceColor.EMPTY:
            return

        self.board_state[row][col] = color

        # 更新最后落子位置
        if self.last_move_pos:
            self._remove_last_move_indicator()
        self.last_move_pos = (row, col)

        if animated and ENABLE_ANIMATIONS:
            # 使用动画放置棋子
            x, y = self._board_to_canvas(row, col)
            radius = self.cell_size // 3
            piece_color = self.theme['black_piece'] if color == PieceColor.BLACK else self.theme['white_piece']

            def on_animation_complete():
                # 确保在动画完成后绘制最终棋子
                try:
                    # 先清除可能存在的旧棋子（避免重复）
                    self.canvas.delete(f'piece_{row}_{col}')

                    # 绘制新棋子
                    self._draw_piece(row, col, color)
                    self._draw_last_move_indicator(row, col)

                    # 强制更新画布
                    self.canvas.update_idletasks()

                    print(f"✓ 动画完成，棋子已绘制在位置 ({row}, {col})")  # 调试信息
                except Exception as e:
                    print(f"✗ 动画完成回调出错: {e}")
                    # 如果出错，确保至少绘制基本棋子
                    try:
                        self._draw_piece(row, col, color)
                        self._draw_last_move_indicator(row, col)
                    except Exception as e2:
                        print(f"✗ 备用绘制也失败: {e2}")

            self.animation_manager.animate_piece_drop(x, y, piece_color, radius, on_animation_complete)
        else:
            # 直接绘制棋子
            self._draw_piece(row, col, color)
            self._draw_last_move_indicator(row, col)

    def _draw_piece(self, row: int, col: int, color: PieceColor):
        """
        绘制棋子（增强立体效果）

        Args:
            row: 行坐标
            col: 列坐标
            color: 棋子颜色
        """
        x, y = self._board_to_canvas(row, col)
        radius = self.cell_size // 3

        piece_color = self.theme['black_piece'] if color == PieceColor.BLACK else self.theme['white_piece']
        outline_color = self.theme['board_lines']

        # 绘制阴影（在棋子下方）
        shadow_offset = 2
        self.canvas.create_oval(
            x - radius + shadow_offset, y - radius + shadow_offset,
            x + radius + shadow_offset, y + radius + shadow_offset,
            fill='#00000020',  # 半透明黑色阴影
            outline='',
            tags=f'piece_{row}_{col}'
        )

        # 绘制棋子主体
        self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            fill=piece_color,
            outline=outline_color,
            width=1,
            tags=f'piece_{row}_{col}'
        )

        # 增强的立体效果
        if color == PieceColor.BLACK:
            # 黑棋：添加多层高光效果
            # 主高光
            self.canvas.create_oval(
                x - radius//2, y - radius//2,
                x - radius//4, y - radius//4,
                fill='#606060',
                outline='',
                tags=f'piece_{row}_{col}'
            )
            # 小高光点
            self.canvas.create_oval(
                x - radius//3, y - radius//3,
                x - radius//5, y - radius//5,
                fill='#808080',
                outline='',
                tags=f'piece_{row}_{col}'
            )
        else:
            # 白棋：添加渐变阴影效果
            # 主阴影
            self.canvas.create_oval(
                x + radius//4, y + radius//4,
                x + radius//2, y + radius//2,
                fill='#D0D0D0',
                outline='',
                tags=f'piece_{row}_{col}'
            )
            # 边缘阴影
            self.canvas.create_oval(
                x + radius//3, y + radius//3,
                x + radius//2.5, y + radius//2.5,
                fill='#C0C0C0',
                outline='',
                tags=f'piece_{row}_{col}'
            )
            # 高光
            self.canvas.create_oval(
                x - radius//2.5, y - radius//2.5,
                x - radius//4, y - radius//4,
                fill='#FFFFFF',
                outline='',
                tags=f'piece_{row}_{col}'
            )

    def _draw_last_move_indicator(self, row: int, col: int):
        """绘制最后落子指示器"""
        x, y = self._board_to_canvas(row, col)
        radius = 4

        self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            fill=self.theme['accent_color'],
            outline='',
            tags='last_move_indicator'
        )

    def _remove_last_move_indicator(self):
        """移除最后落子指示器"""
        self.canvas.delete('last_move_indicator')

    def remove_piece(self, row: int, col: int):
        """
        移除指定位置的棋子

        Args:
            row: 行坐标
            col: 列坐标
        """
        self.board_state[row][col] = PieceColor.EMPTY
        self.canvas.delete(f'piece_{row}_{col}')

        # 如果移除的是最后落子，清除指示器
        if self.last_move_pos == (row, col):
            self._remove_last_move_indicator()
            self.last_move_pos = None

    def draw_win_line(self, win_line: List[Tuple[int, int]], animated: bool = True):
        """
        绘制获胜连线（支持动画）

        Args:
            win_line: 获胜连线的坐标列表
            animated: 是否使用动画
        """
        self.win_line = win_line
        if len(win_line) < 2:
            return

        # 计算连线的起点和终点
        start_pos = self._board_to_canvas(win_line[0][0], win_line[0][1])
        end_pos = self._board_to_canvas(win_line[-1][0], win_line[-1][1])

        if animated and ENABLE_ANIMATIONS:
            # 使用动画绘制获胜连线
            def on_line_complete():
                # 连线动画完成后，添加庆祝效果
                center_x = (start_pos[0] + end_pos[0]) // 2
                center_y = (start_pos[1] + end_pos[1]) // 2
                self.animation_manager.animate_celebration(center_x, center_y)

            self.animation_manager.animate_win_line(start_pos, end_pos, on_line_complete)
        else:
            # 直接绘制获胜连线
            self.canvas.create_line(
                start_pos[0], start_pos[1],
                end_pos[0], end_pos[1],
                fill=self.theme['win_line_color'],
                width=4,
                tags='win_line'
            )

    def clear_board(self):
        """清空棋盘"""
        # 停止所有动画
        self.animation_manager.stop_all_animations()

        self.board_state = [[PieceColor.EMPTY for _ in range(self.board_size)] for _ in range(self.board_size)]
        self.last_move_pos = None
        self.win_line = []
        self.hover_pos = None

        # 清除所有棋子和指示器
        self.canvas.delete('piece')
        for row in range(self.board_size):
            for col in range(self.board_size):
                self.canvas.delete(f'piece_{row}_{col}')
        self.canvas.delete('last_move_indicator')
        self.canvas.delete('win_line')
        self.canvas.delete('hover_indicator')

    def set_current_player(self, color: PieceColor):
        """设置当前玩家颜色"""
        self.current_player_color = color

    def get_widget(self) -> Canvas:
        """获取画布组件"""
        return self.canvas
