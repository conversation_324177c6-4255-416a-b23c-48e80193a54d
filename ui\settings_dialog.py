"""
游戏设置对话框
包含棋盘大小选择、游戏模式选择、AI难度设置等
"""

import tkinter as tk
from typing import Dict, Any, Optional
from config.settings import (
    BOARD_SIZE_OPTIONS, DEFAULT_GAME_MODE, AI_DIFFICULTY_LEVELS,
    DEFAULT_AI_DIFFICULTY, CURRENT_THEME, DEFAULT_THEME
)
from ui.styles import style_manager
from ui.theme_manager import theme_manager, ThemePreviewWidget

class SettingsDialog:
    """游戏设置对话框类"""

    def __init__(self, parent: tk.Tk, current_settings: Dict[str, Any]):
        """
        初始化设置对话框

        Args:
            parent: 父窗口
            current_settings: 当前设置
        """
        self.parent = parent
        self.current_settings = current_settings.copy()
        self.result: Optional[Dict[str, Any]] = None
        self.theme = CURRENT_THEME

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("游戏设置")
        self.dialog.configure(bg=self.theme['interface_bg'])
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 创建界面
        self._create_widgets()

        # 计算并设置合适的窗口大小
        self._calculate_and_set_size()

        # 居中显示
        self._center_dialog()

        # 加载当前设置
        self._load_current_settings()

    def _calculate_and_set_size(self):
        """计算并设置合适的窗口大小"""
        self.dialog.update_idletasks()

        # 获取内容的实际大小
        req_width = self.dialog.winfo_reqwidth()
        req_height = self.dialog.winfo_reqheight()

        # 设置最小尺寸和合适的边距
        min_width = max(450, req_width + 40)  # 最小宽度450px，加40px边距
        min_height = max(550, req_height + 40)  # 最小高度550px，加40px边距

        # 设置窗口大小
        self.dialog.geometry(f"{min_width}x{min_height}")
        self.dialog_width = min_width
        self.dialog_height = min_height

    def _center_dialog(self):
        """将对话框居中显示"""
        self.dialog.update_idletasks()

        # 获取父窗口的位置和大小
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # 计算居中位置
        x = parent_x + (parent_width // 2) - (self.dialog_width // 2)
        y = parent_y + (parent_height // 2) - (self.dialog_height // 2)

        # 确保对话框不会超出屏幕边界
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()

        x = max(0, min(x, screen_width - self.dialog_width))
        y = max(0, min(y, screen_height - self.dialog_height))

        self.dialog.geometry(f"{self.dialog_width}x{self.dialog_height}+{x}+{y}")

    def _create_widgets(self):
        """创建界面组件"""
        # 主框架 - 使用滚动框架以防内容过多
        main_frame = tk.Frame(self.dialog, bg=self.theme['interface_bg'])
        main_frame.pack(fill='both', expand=True, padx=25, pady=25)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="游戏设置",
            **style_manager.get_label_style('title')
        )
        title_label.pack(pady=(0, 25))

        # 创建内容区域
        content_frame = tk.Frame(main_frame, bg=self.theme['interface_bg'])
        content_frame.pack(fill='both', expand=True)

        # 棋盘大小设置
        self._create_board_size_section(content_frame)

        # 游戏模式设置
        self._create_game_mode_section(content_frame)

        # AI难度设置
        self._create_ai_difficulty_section(content_frame)

        # 主题设置
        self._create_theme_section(content_frame)

        # 分隔线
        separator = tk.Frame(main_frame, height=2, bg=self.theme['board_lines'])
        separator.pack(fill='x', pady=(20, 15))

        # 按钮区域
        self._create_buttons(main_frame)

    def _create_board_size_section(self, parent):
        """创建棋盘大小设置区域"""
        # 分组框架
        board_frame = tk.LabelFrame(
            parent,
            text=" 棋盘大小 ",
            bg=self.theme['interface_bg'],
            fg=self.theme['text_color'],
            font=style_manager.fonts['large'],
            padx=15,
            pady=10
        )
        board_frame.pack(fill='x', pady=(0, 20))

        # 棋盘大小选择 - 使用网格布局
        self.board_size_var = tk.IntVar()

        # 创建内部框架用于更好的布局
        size_inner_frame = tk.Frame(board_frame, bg=self.theme['interface_bg'])
        size_inner_frame.pack(fill='x', padx=5, pady=5)

        for size in BOARD_SIZE_OPTIONS:
            radio = tk.Radiobutton(
                size_inner_frame,
                text=f"{size}×{size} 棋盘",
                variable=self.board_size_var,
                value=size,
                bg=self.theme['interface_bg'],
                fg=self.theme['text_color'],
                selectcolor=self.theme['accent_color'],
                font=style_manager.fonts['normal'],
                anchor='w'
            )
            radio.pack(anchor='w', padx=10, pady=4)

    def _create_game_mode_section(self, parent):
        """创建游戏模式设置区域"""
        # 分组框架
        mode_frame = tk.LabelFrame(
            parent,
            text=" 游戏模式 ",
            bg=self.theme['interface_bg'],
            fg=self.theme['text_color'],
            font=style_manager.fonts['large'],
            padx=15,
            pady=10
        )
        mode_frame.pack(fill='x', pady=(0, 20))

        # 游戏模式选择
        self.game_mode_var = tk.StringVar()

        # 创建内部框架
        mode_inner_frame = tk.Frame(mode_frame, bg=self.theme['interface_bg'])
        mode_inner_frame.pack(fill='x', padx=5, pady=5)

        # 双人对战
        pvp_frame = tk.Frame(mode_inner_frame, bg=self.theme['interface_bg'])
        pvp_frame.pack(fill='x', pady=2)

        pvp_radio = tk.Radiobutton(
            pvp_frame,
            text="双人对战",
            variable=self.game_mode_var,
            value='pvp',
            command=self._on_mode_change,
            bg=self.theme['interface_bg'],
            fg=self.theme['text_color'],
            selectcolor=self.theme['accent_color'],
            font=style_manager.fonts['normal'],
            anchor='w'
        )
        pvp_radio.pack(side='left', padx=10)

        pvp_desc = tk.Label(
            pvp_frame,
            text="本地双人轮流对弈",
            bg=self.theme['interface_bg'],
            fg='#666666',
            font=(style_manager.fonts['normal'][0], 9)
        )
        pvp_desc.pack(side='left', padx=(5, 0))

        # 人机对战
        pve_frame = tk.Frame(mode_inner_frame, bg=self.theme['interface_bg'])
        pve_frame.pack(fill='x', pady=2)

        pve_radio = tk.Radiobutton(
            pve_frame,
            text="人机对战",
            variable=self.game_mode_var,
            value='pve',
            command=self._on_mode_change,
            bg=self.theme['interface_bg'],
            fg=self.theme['text_color'],
            selectcolor=self.theme['accent_color'],
            font=style_manager.fonts['normal'],
            anchor='w'
        )
        pve_radio.pack(side='left', padx=10)

        pve_desc = tk.Label(
            pve_frame,
            text="与AI智能对手对弈",
            bg=self.theme['interface_bg'],
            fg='#666666',
            font=(style_manager.fonts['normal'][0], 9)
        )
        pve_desc.pack(side='left', padx=(5, 0))

    def _create_ai_difficulty_section(self, parent):
        """创建AI难度设置区域"""
        # 分组框架
        self.ai_frame = tk.LabelFrame(
            parent,
            text=" AI难度 ",
            bg=self.theme['interface_bg'],
            fg=self.theme['text_color'],
            font=style_manager.fonts['large'],
            padx=15,
            pady=10
        )
        self.ai_frame.pack(fill='x', pady=(0, 20))

        # AI难度选择
        self.ai_difficulty_var = tk.StringVar()

        # 创建内部框架
        ai_inner_frame = tk.Frame(self.ai_frame, bg=self.theme['interface_bg'])
        ai_inner_frame.pack(fill='x', padx=5, pady=5)

        for difficulty, info in AI_DIFFICULTY_LEVELS.items():
            # 为每个难度创建一个框架
            diff_frame = tk.Frame(ai_inner_frame, bg=self.theme['interface_bg'])
            diff_frame.pack(fill='x', pady=3)

            radio = tk.Radiobutton(
                diff_frame,
                text=info['name'],
                variable=self.ai_difficulty_var,
                value=difficulty,
                bg=self.theme['interface_bg'],
                fg=self.theme['text_color'],
                selectcolor=self.theme['accent_color'],
                font=style_manager.fonts['normal'],
                anchor='w'
            )
            radio.pack(side='left', padx=10)

            # 添加描述标签
            desc_label = tk.Label(
                diff_frame,
                text=f"- {info['description']}",
                bg=self.theme['interface_bg'],
                fg='#666666',
                font=(style_manager.fonts['normal'][0], 9)
            )
            desc_label.pack(side='left', padx=(5, 0))

    def _create_theme_section(self, parent):
        """创建主题设置区域"""
        # 分组框架
        theme_frame = tk.LabelFrame(
            parent,
            text=" 界面主题 ",
            bg=self.theme['interface_bg'],
            fg=self.theme['text_color'],
            font=style_manager.fonts['large'],
            padx=15,
            pady=10
        )
        theme_frame.pack(fill='x', pady=(0, 20))

        # 主题选择
        self.theme_var = tk.StringVar()

        # 创建内部框架
        theme_inner_frame = tk.Frame(theme_frame, bg=self.theme['interface_bg'])
        theme_inner_frame.pack(fill='x', padx=5, pady=5)

        available_themes = theme_manager.get_available_themes()

        for theme_key, theme_name in available_themes.items():
            # 为每个主题创建一个框架
            theme_item_frame = tk.Frame(theme_inner_frame, bg=self.theme['interface_bg'])
            theme_item_frame.pack(fill='x', pady=5)

            # 主题选择单选按钮
            radio = tk.Radiobutton(
                theme_item_frame,
                text=theme_name,
                variable=self.theme_var,
                value=theme_key,
                bg=self.theme['interface_bg'],
                fg=self.theme['text_color'],
                selectcolor=self.theme['accent_color'],
                font=style_manager.fonts['normal'],
                anchor='w'
            )
            radio.pack(side='left', padx=10)

            # 主题预览
            preview = ThemePreviewWidget(theme_item_frame, theme_key, theme_manager)
            preview.pack(side='right', padx=(5, 10))

    def _create_buttons(self, parent):
        """创建按钮区域"""
        button_frame = tk.Frame(parent, bg=self.theme['interface_bg'])
        button_frame.pack(fill='x', pady=(25, 0))

        # 左侧按钮区域
        left_button_frame = tk.Frame(button_frame, bg=self.theme['interface_bg'])
        left_button_frame.pack(side='left')

        # 恢复默认按钮
        default_button = style_manager.create_rounded_button(
            left_button_frame,
            text="恢复默认",
            command=self._on_default,
            button_type='normal'
        )
        default_button.pack(side='left')

        # 右侧按钮区域
        right_button_frame = tk.Frame(button_frame, bg=self.theme['interface_bg'])
        right_button_frame.pack(side='right')

        # 取消按钮
        cancel_button = style_manager.create_rounded_button(
            right_button_frame,
            text="取消",
            command=self._on_cancel,
            button_type='normal'
        )
        cancel_button.pack(side='left', padx=(0, 10))

        # 确定按钮
        ok_button = style_manager.create_rounded_button(
            right_button_frame,
            text="确定",
            command=self._on_ok,
            button_type='primary'
        )
        ok_button.pack(side='left')

    def _load_current_settings(self):
        """加载当前设置"""
        # 设置棋盘大小
        self.board_size_var.set(self.current_settings.get('board_size', 15))

        # 设置游戏模式
        self.game_mode_var.set(self.current_settings.get('game_mode', DEFAULT_GAME_MODE))

        # 设置AI难度
        self.ai_difficulty_var.set(self.current_settings.get('ai_difficulty', DEFAULT_AI_DIFFICULTY))

        # 设置主题
        self.theme_var.set(self.current_settings.get('theme', DEFAULT_THEME))

        # 更新AI设置区域的可见性
        self._on_mode_change()

    def _on_mode_change(self):
        """游戏模式改变时的处理"""
        if self.game_mode_var.get() == 'pve':
            # 启用AI难度设置
            self._set_ai_frame_state('normal')
        else:
            # 禁用AI难度设置
            self._set_ai_frame_state('disabled')

    def _set_ai_frame_state(self, state):
        """设置AI设置区域的状态"""
        def set_widget_state(widget):
            try:
                if isinstance(widget, (tk.Radiobutton, tk.Label)):
                    widget.configure(state=state)
                # 递归处理子组件
                for child in widget.winfo_children():
                    set_widget_state(child)
            except tk.TclError:
                pass  # 忽略不支持state属性的组件

        set_widget_state(self.ai_frame)

    def _on_ok(self):
        """确定按钮点击处理"""
        self.result = {
            'board_size': self.board_size_var.get(),
            'game_mode': self.game_mode_var.get(),
            'ai_difficulty': self.ai_difficulty_var.get(),
            'theme': self.theme_var.get()
        }
        self.dialog.destroy()

    def _on_cancel(self):
        """取消按钮点击处理"""
        self.result = None
        self.dialog.destroy()

    def _on_default(self):
        """恢复默认按钮点击处理"""
        self.board_size_var.set(15)
        self.game_mode_var.set(DEFAULT_GAME_MODE)
        self.ai_difficulty_var.set(DEFAULT_AI_DIFFICULTY)
        self.theme_var.set(DEFAULT_THEME)
        self._on_mode_change()

    def show(self) -> Optional[Dict[str, Any]]:
        """
        显示对话框并等待用户操作

        Returns:
            Optional[Dict[str, Any]]: 用户选择的设置，如果取消则返回None
        """
        self.dialog.wait_window()
        return self.result

def show_settings_dialog(parent: tk.Tk, current_settings: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    显示设置对话框的便捷函数

    Args:
        parent: 父窗口
        current_settings: 当前设置

    Returns:
        Optional[Dict[str, Any]]: 用户选择的设置
    """
    dialog = SettingsDialog(parent, current_settings)
    return dialog.show()
