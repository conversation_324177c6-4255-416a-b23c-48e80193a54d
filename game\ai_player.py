"""
AI玩家模块
实现不同难度的AI对弈算法
"""

import random
import math
from typing import Tuple, List, Optional
from .player import PieceColor
from .board import GameBoard
from config.settings import AI_DIFFICULTY_LEVELS

class AIPlayer:
    """AI玩家类"""
    
    def __init__(self, color: PieceColor, difficulty: str = 'medium'):
        """
        初始化AI玩家
        
        Args:
            color: AI棋子颜色
            difficulty: 难度级别 ('easy', 'medium', 'hard', 'expert')
        """
        self.color = color
        self.opponent_color = PieceColor.WHITE if color == PieceColor.BLACK else PieceColor.BLACK
        self.difficulty = difficulty
        self.search_depth = AI_DIFFICULTY_LEVELS[difficulty]['depth']
        
        # 评分权重
        self.weights = {
            'win': 100000,      # 获胜
            'block_win': 50000, # 阻止对手获胜
            'four': 10000,      # 活四
            'block_four': 5000, # 阻止对手活四
            'three': 1000,      # 活三
            'block_three': 500, # 阻止对手活三
            'two': 100,         # 活二
            'block_two': 50,    # 阻止对手活二
            'center': 10        # 中心位置加分
        }
    
    def get_best_move(self, board: GameBoard) -> Optional[Tuple[int, int]]:
        """
        获取最佳落子位置
        
        Args:
            board: 当前棋盘状态
            
        Returns:
            Optional[Tuple[int, int]]: 最佳落子位置，如果没有则返回None
        """
        if board.get_move_count() == 0:
            # 第一步下在中心附近
            center = board.size // 2
            return (center, center)
        
        # 获取所有可能的落子位置
        candidates = self._get_candidate_moves(board)
        if not candidates:
            return None
        
        if self.difficulty == 'easy':
            return self._get_easy_move(board, candidates)
        else:
            return self._get_minimax_move(board, candidates)
    
    def _get_candidate_moves(self, board: GameBoard) -> List[Tuple[int, int]]:
        """
        获取候选落子位置（在已有棋子周围）
        
        Args:
            board: 棋盘状态
            
        Returns:
            List[Tuple[int, int]]: 候选位置列表
        """
        candidates = set()
        
        # 在已有棋子周围2格范围内寻找空位
        for row in range(board.size):
            for col in range(board.size):
                if board.get_piece(row, col) != PieceColor.EMPTY:
                    # 在周围添加候选位置
                    for dr in range(-2, 3):
                        for dc in range(-2, 3):
                            new_row, new_col = row + dr, col + dc
                            if (board.is_valid_position(new_row, new_col) and 
                                board.is_empty(new_row, new_col)):
                                candidates.add((new_row, new_col))
        
        return list(candidates)
    
    def _get_easy_move(self, board: GameBoard, candidates: List[Tuple[int, int]]) -> Tuple[int, int]:
        """
        简单难度：随机选择 + 基本防守
        
        Args:
            board: 棋盘状态
            candidates: 候选位置
            
        Returns:
            Tuple[int, int]: 选择的位置
        """
        # 检查是否有立即获胜的机会
        for row, col in candidates:
            if self._is_winning_move(board, row, col, self.color):
                return (row, col)
        
        # 检查是否需要阻止对手获胜
        for row, col in candidates:
            if self._is_winning_move(board, row, col, self.opponent_color):
                return (row, col)
        
        # 随机选择
        return random.choice(candidates)
    
    def _get_minimax_move(self, board: GameBoard, candidates: List[Tuple[int, int]]) -> Tuple[int, int]:
        """
        使用Minimax算法选择最佳位置
        
        Args:
            board: 棋盘状态
            candidates: 候选位置
            
        Returns:
            Tuple[int, int]: 最佳位置
        """
        best_score = float('-inf')
        best_move = candidates[0]
        
        # 限制候选位置数量以提高性能
        if len(candidates) > 20:
            # 按评分排序，只考虑前20个位置
            candidates = sorted(candidates, 
                              key=lambda pos: self._evaluate_position(board, pos[0], pos[1], self.color),
                              reverse=True)[:20]
        
        for row, col in candidates:
            # 尝试落子
            board.place_piece(row, col, self.color)
            
            # 计算分数
            score = self._minimax(board, self.search_depth - 1, False, float('-inf'), float('inf'))
            
            # 撤销落子
            board.undo_last_move()
            
            if score > best_score:
                best_score = score
                best_move = (row, col)
        
        return best_move
    
    def _minimax(self, board: GameBoard, depth: int, is_maximizing: bool, alpha: float, beta: float) -> float:
        """
        Minimax算法实现（带Alpha-Beta剪枝）
        
        Args:
            board: 棋盘状态
            depth: 搜索深度
            is_maximizing: 是否为最大化玩家
            alpha: Alpha值
            beta: Beta值
            
        Returns:
            float: 评估分数
        """
        # 检查游戏是否结束
        if depth == 0 or self._is_game_over(board):
            return self._evaluate_board(board)
        
        candidates = self._get_candidate_moves(board)
        if not candidates:
            return self._evaluate_board(board)
        
        # 限制候选位置数量
        if len(candidates) > 10:
            current_color = self.color if is_maximizing else self.opponent_color
            candidates = sorted(candidates,
                              key=lambda pos: self._evaluate_position(board, pos[0], pos[1], current_color),
                              reverse=True)[:10]
        
        if is_maximizing:
            max_score = float('-inf')
            for row, col in candidates:
                board.place_piece(row, col, self.color)
                score = self._minimax(board, depth - 1, False, alpha, beta)
                board.undo_last_move()
                
                max_score = max(max_score, score)
                alpha = max(alpha, score)
                if beta <= alpha:
                    break  # Alpha-Beta剪枝
            return max_score
        else:
            min_score = float('inf')
            for row, col in candidates:
                board.place_piece(row, col, self.opponent_color)
                score = self._minimax(board, depth - 1, True, alpha, beta)
                board.undo_last_move()
                
                min_score = min(min_score, score)
                beta = min(beta, score)
                if beta <= alpha:
                    break  # Alpha-Beta剪枝
            return min_score
    
    def _evaluate_board(self, board: GameBoard) -> float:
        """
        评估整个棋盘的分数
        
        Args:
            board: 棋盘状态
            
        Returns:
            float: 评估分数
        """
        ai_score = self._evaluate_player(board, self.color)
        opponent_score = self._evaluate_player(board, self.opponent_color)
        return ai_score - opponent_score
    
    def _evaluate_player(self, board: GameBoard, color: PieceColor) -> float:
        """
        评估某个玩家的分数
        
        Args:
            board: 棋盘状态
            color: 玩家颜色
            
        Returns:
            float: 玩家分数
        """
        score = 0
        
        # 检查所有方向的连子情况
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
        
        for row in range(board.size):
            for col in range(board.size):
                if board.get_piece(row, col) == color:
                    for dx, dy in directions:
                        score += self._evaluate_line(board, row, col, dx, dy, color)
        
        return score
    
    def _evaluate_line(self, board: GameBoard, row: int, col: int, dx: int, dy: int, color: PieceColor) -> float:
        """
        评估某个方向上的连子分数
        
        Args:
            board: 棋盘状态
            row, col: 起始位置
            dx, dy: 方向向量
            color: 棋子颜色
            
        Returns:
            float: 该方向的分数
        """
        count = 1  # 包括起始位置
        blocked = 0  # 被阻挡的端点数
        
        # 向正方向搜索
        r, c = row + dx, col + dy
        while board.is_valid_position(r, c) and board.get_piece(r, c) == color:
            count += 1
            r, c = r + dx, c + dy
        
        if not board.is_valid_position(r, c) or board.get_piece(r, c) != PieceColor.EMPTY:
            blocked += 1
        
        # 向负方向搜索
        r, c = row - dx, col - dy
        while board.is_valid_position(r, c) and board.get_piece(r, c) == color:
            count += 1
            r, c = r - dx, c - dy
        
        if not board.is_valid_position(r, c) or board.get_piece(r, c) != PieceColor.EMPTY:
            blocked += 1
        
        # 根据连子数和阻挡情况评分
        if count >= 5:
            return self.weights['win']
        elif count == 4:
            if blocked == 0:
                return self.weights['four']
            elif blocked == 1:
                return self.weights['four'] // 2
        elif count == 3:
            if blocked == 0:
                return self.weights['three']
            elif blocked == 1:
                return self.weights['three'] // 2
        elif count == 2:
            if blocked == 0:
                return self.weights['two']
        
        return 0
    
    def _evaluate_position(self, board: GameBoard, row: int, col: int, color: PieceColor) -> float:
        """
        评估单个位置的价值
        
        Args:
            board: 棋盘状态
            row, col: 位置坐标
            color: 棋子颜色
            
        Returns:
            float: 位置价值
        """
        if not board.is_empty(row, col):
            return 0
        
        # 临时放置棋子
        board.place_piece(row, col, color)
        score = self._evaluate_player(board, color)
        board.undo_last_move()
        
        # 中心位置加分
        center = board.size // 2
        distance_to_center = abs(row - center) + abs(col - center)
        center_bonus = max(0, self.weights['center'] - distance_to_center)
        
        return score + center_bonus
    
    def _is_winning_move(self, board: GameBoard, row: int, col: int, color: PieceColor) -> bool:
        """
        检查某个位置是否为获胜落子
        
        Args:
            board: 棋盘状态
            row, col: 位置坐标
            color: 棋子颜色
            
        Returns:
            bool: 是否为获胜落子
        """
        if not board.is_empty(row, col):
            return False
        
        board.place_piece(row, col, color)
        is_winning = self._check_win_at_position(board, row, col, color)
        board.undo_last_move()
        
        return is_winning
    
    def _check_win_at_position(self, board: GameBoard, row: int, col: int, color: PieceColor) -> bool:
        """
        检查指定位置是否形成获胜连线
        
        Args:
            board: 棋盘状态
            row, col: 位置坐标
            color: 棋子颜色
            
        Returns:
            bool: 是否获胜
        """
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
        
        for dx, dy in directions:
            count = 1  # 包括当前位置
            
            # 向正方向计数
            r, c = row + dx, col + dy
            while board.is_valid_position(r, c) and board.get_piece(r, c) == color:
                count += 1
                r, c = r + dx, c + dy
            
            # 向负方向计数
            r, c = row - dx, col - dy
            while board.is_valid_position(r, c) and board.get_piece(r, c) == color:
                count += 1
                r, c = r - dx, c - dy
            
            if count >= 5:
                return True
        
        return False
    
    def _is_game_over(self, board: GameBoard) -> bool:
        """
        检查游戏是否结束
        
        Args:
            board: 棋盘状态
            
        Returns:
            bool: 游戏是否结束
        """
        if not board.move_history:
            return False
        
        last_move = board.move_history[-1]
        return (self._check_win_at_position(board, last_move.row, last_move.col, last_move.color) or
                board.is_board_full())
