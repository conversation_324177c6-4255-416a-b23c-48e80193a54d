"""
游戏核心逻辑模块
包含胜负判断、游戏状态管理等核心功能
"""

from typing import List, Tuple, Optional
from enum import Enum
from .board import GameBoard
from .player import GamePlayers, PieceColor
from .ai_player import AIPlayer
from config.settings import WIN_COUNT, BOARD_SIZE, DEFAULT_AI_DIFFICULTY

class GameState(Enum):
    """游戏状态枚举"""
    WAITING = "waiting"      # 等待开始
    PLAYING = "playing"      # 游戏进行中
    BLACK_WIN = "black_win"  # 黑棋获胜
    WHITE_WIN = "white_win"  # 白棋获胜
    DRAW = "draw"           # 平局
    PAUSED = "paused"       # 暂停

class WinInfo:
    """获胜信息类"""

    def __init__(self, winner: PieceColor, win_line: List[Tuple[int, int]]):
        """
        初始化获胜信息

        Args:
            winner: 获胜方颜色
            win_line: 获胜连线的坐标列表
        """
        self.winner = winner
        self.win_line = win_line

class GameLogic:
    """游戏逻辑管理器"""

    def __init__(self, board_size: int = BOARD_SIZE):
        """初始化游戏逻辑"""
        self.board_size = board_size
        self.board = GameBoard(board_size)
        self.players = GamePlayers()
        self.state = GameState.WAITING
        self.win_info: Optional[WinInfo] = None
        self.undo_count = 0  # 悔棋次数

        # AI相关
        self.game_mode = 'pvp'  # 'pvp' 或 'pve'
        self.ai_player: Optional[AIPlayer] = None
        self.ai_difficulty = DEFAULT_AI_DIFFICULTY

    def set_game_mode(self, mode: str, ai_difficulty: str = None):
        """
        设置游戏模式

        Args:
            mode: 游戏模式 ('pvp' 或 'pve')
            ai_difficulty: AI难度 (仅在pve模式下有效)
        """
        self.game_mode = mode
        if mode == 'pve':
            if ai_difficulty:
                self.ai_difficulty = ai_difficulty
            # 创建AI玩家（白棋）
            self.ai_player = AIPlayer(PieceColor.WHITE, self.ai_difficulty)
        else:
            self.ai_player = None

    def set_board_size(self, size: int):
        """
        设置棋盘大小

        Args:
            size: 棋盘大小
        """
        self.board_size = size
        self.board = GameBoard(size)

    def start_new_game(self):
        """开始新游戏"""
        self.board.clear_board()
        self.players.reset_to_first_player()
        self.state = GameState.PLAYING
        self.win_info = None
        self.undo_count = 0

    def make_move(self, row: int, col: int) -> bool:
        """
        执行落子

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 是否成功落子
        """
        if self.state != GameState.PLAYING:
            return False

        current_player = self.players.get_current_player()
        if not self.board.place_piece(row, col, current_player.piece_color):
            return False

        # 检查是否获胜
        win_info = self.check_win(row, col, current_player.piece_color)
        if win_info:
            self.win_info = win_info
            self.state = GameState.BLACK_WIN if win_info.winner == PieceColor.BLACK else GameState.WHITE_WIN
            current_player.add_win()
        elif self.board.is_board_full():
            self.state = GameState.DRAW
        else:
            # 切换玩家
            self.players.switch_player()

        return True

    def get_ai_move(self) -> Optional[Tuple[int, int]]:
        """
        获取AI的落子位置

        Returns:
            Optional[Tuple[int, int]]: AI选择的位置，如果无法落子则返回None
        """
        if not self.ai_player or self.game_mode != 'pve':
            return None

        return self.ai_player.get_best_move(self.board)

    def is_ai_turn(self) -> bool:
        """
        检查是否轮到AI落子

        Returns:
            bool: 是否轮到AI
        """
        if self.game_mode != 'pve' or not self.ai_player:
            return False

        current_player = self.players.get_current_player()
        return current_player.piece_color == self.ai_player.color

    def undo_move(self) -> bool:
        """
        悔棋

        Returns:
            bool: 是否成功悔棋
        """
        if self.state != GameState.PLAYING or not self.board.move_history:
            return False

        if self.board.undo_last_move():
            self.players.switch_player()  # 切换回上一个玩家
            self.undo_count += 1
            return True
        return False

    def check_win(self, row: int, col: int, color: PieceColor) -> Optional[WinInfo]:
        """
        检查是否获胜

        Args:
            row: 最后落子的行坐标
            col: 最后落子的列坐标
            color: 棋子颜色

        Returns:
            Optional[WinInfo]: 获胜信息，如果没有获胜则返回None
        """
        directions = [
            (0, 1),   # 水平
            (1, 0),   # 垂直
            (1, 1),   # 主对角线
            (1, -1)   # 副对角线
        ]

        for dx, dy in directions:
            line = self._get_line_pieces(row, col, dx, dy, color)
            if len(line) >= WIN_COUNT:
                return WinInfo(color, line[:WIN_COUNT])

        return None

    def _get_line_pieces(self, row: int, col: int, dx: int, dy: int, color: PieceColor) -> List[Tuple[int, int]]:
        """
        获取指定方向上连续的同色棋子

        Args:
            row: 起始行坐标
            col: 起始列坐标
            dx: 行方向增量
            dy: 列方向增量
            color: 棋子颜色

        Returns:
            List[Tuple[int, int]]: 连续同色棋子的坐标列表
        """
        line = [(row, col)]

        # 向正方向搜索
        r, c = row + dx, col + dy
        while self.board.is_valid_position(r, c) and self.board.get_piece(r, c) == color:
            line.append((r, c))
            r, c = r + dx, c + dy

        # 向负方向搜索
        r, c = row - dx, col - dy
        while self.board.is_valid_position(r, c) and self.board.get_piece(r, c) == color:
            line.insert(0, (r, c))
            r, c = r - dx, c - dy

        return line

    def get_current_player_name(self) -> str:
        """获取当前玩家名称"""
        return self.players.get_current_player().get_color_name()

    def get_game_status_text(self) -> str:
        """获取游戏状态文本"""
        if self.state == GameState.WAITING:
            return "点击开始新游戏"
        elif self.state == GameState.PLAYING:
            return f"{self.get_current_player_name()}的回合"
        elif self.state == GameState.BLACK_WIN:
            return "黑棋获胜！"
        elif self.state == GameState.WHITE_WIN:
            return "白棋获胜！"
        elif self.state == GameState.DRAW:
            return "平局！"
        else:
            return "游戏暂停"

    def is_game_over(self) -> bool:
        """检查游戏是否结束"""
        return self.state in [GameState.BLACK_WIN, GameState.WHITE_WIN, GameState.DRAW]

    def can_undo(self) -> bool:
        """检查是否可以悔棋"""
        return (self.state == GameState.PLAYING and
                len(self.board.move_history) > 0)

    def get_last_move(self) -> Optional[Tuple[int, int]]:
        """获取最后一步落子位置"""
        if self.board.last_move:
            return (self.board.last_move.row, self.board.last_move.col)
        return None
