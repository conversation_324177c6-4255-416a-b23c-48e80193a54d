#!/usr/bin/env python3
"""
测试导入模块
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("测试导入模块...")
    
    # 测试配置模块
    from config.settings import BOARD_SIZE, CURRENT_THEME
    print(f"✓ 配置模块导入成功 - 棋盘大小: {BOARD_SIZE}")
    
    # 测试游戏逻辑模块
    from game.player import PieceColor, Player
    print("✓ 玩家模块导入成功")
    
    from game.board import GameBoard
    print("✓ 棋盘模块导入成功")
    
    from game.game_logic import GameLogic
    print("✓ 游戏逻辑模块导入成功")
    
    # 测试UI模块
    from ui.styles import style_manager
    print("✓ 样式模块导入成功")
    
    from ui.game_board_ui import GameBoardUI
    print("✓ 棋盘UI模块导入成功")
    
    from ui.control_panel import ControlPanel
    print("✓ 控制面板模块导入成功")
    
    from ui.main_window import MainWindow
    print("✓ 主窗口模块导入成功")
    
    print("\n所有模块导入成功！")
    
    # 测试创建游戏逻辑
    game = GameLogic()
    print(f"✓ 游戏逻辑创建成功 - 状态: {game.state}")
    
    print("\n模块测试完成，准备启动GUI...")
    
except ImportError as e:
    print(f"✗ 导入错误: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"✗ 其他错误: {e}")
    import traceback
    traceback.print_exc()
