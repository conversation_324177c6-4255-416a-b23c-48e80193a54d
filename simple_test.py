print("Starting test...")

try:
    import tkinter as tk
    print("Tkinter imported successfully")
    
    # Test creating a simple window
    root = tk.Tk()
    root.title("Test")
    root.geometry("300x200")
    
    label = tk.Label(root, text="Test Window")
    label.pack(pady=50)
    
    print("Window created successfully")
    
    # Don't show the window, just test creation
    root.destroy()
    print("Test completed successfully")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
