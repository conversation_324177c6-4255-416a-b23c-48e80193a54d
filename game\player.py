"""
玩家管理模块
定义玩家类和相关属性
"""

from enum import Enum
from typing import Optional

class PieceColor(Enum):
    """棋子颜色枚举"""
    EMPTY = 0
    BLACK = 1
    WHITE = 2

class Player:
    """玩家类"""
    
    def __init__(self, name: str, piece_color: PieceColor, avatar: Optional[str] = None):
        """
        初始化玩家
        
        Args:
            name: 玩家姓名
            piece_color: 棋子颜色
            avatar: 头像路径（可选）
        """
        self.name = name
        self.piece_color = piece_color
        self.avatar = avatar
        self.wins = 0  # 胜利次数
        self.total_time = 0  # 总用时（秒）
        self.current_time = 0  # 当前局用时
    
    def __str__(self):
        return f"{self.name} ({self.piece_color.name})"
    
    def get_color_name(self) -> str:
        """获取棋子颜色的中文名称"""
        color_names = {
            PieceColor.BLACK: "黑棋",
            PieceColor.WHITE: "白棋"
        }
        return color_names.get(self.piece_color, "未知")
    
    def add_win(self):
        """增加胜利次数"""
        self.wins += 1
    
    def reset_current_time(self):
        """重置当前局用时"""
        self.current_time = 0

class GamePlayers:
    """游戏玩家管理器"""
    
    def __init__(self):
        """初始化玩家管理器"""
        self.player1 = Player("玩家1", PieceColor.BLACK)
        self.player2 = Player("玩家2", PieceColor.WHITE)
        self.current_player = self.player1
    
    def get_current_player(self) -> Player:
        """获取当前玩家"""
        return self.current_player
    
    def get_opponent(self) -> Player:
        """获取对手玩家"""
        return self.player2 if self.current_player == self.player1 else self.player1
    
    def switch_player(self):
        """切换当前玩家"""
        self.current_player = self.get_opponent()
    
    def reset_to_first_player(self):
        """重置为第一个玩家（黑棋先手）"""
        self.current_player = self.player1
    
    def get_player_by_color(self, color: PieceColor) -> Optional[Player]:
        """根据棋子颜色获取玩家"""
        if self.player1.piece_color == color:
            return self.player1
        elif self.player2.piece_color == color:
            return self.player2
        return None
    
    def set_player_names(self, name1: str, name2: str):
        """设置玩家姓名"""
        self.player1.name = name1
        self.player2.name = name2
