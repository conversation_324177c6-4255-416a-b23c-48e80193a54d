import py_compile
import os

files_to_check = [
    'config/settings.py',
    'game/player.py', 
    'game/board.py',
    'game/game_logic.py',
    'ui/styles.py',
    'ui/game_board_ui.py',
    'ui/control_panel.py',
    'ui/main_window.py',
    'main.py'
]

print("Checking syntax of all Python files...")

for file_path in files_to_check:
    try:
        py_compile.compile(file_path, doraise=True)
        print(f"✓ {file_path} - OK")
    except py_compile.PyCompileError as e:
        print(f"✗ {file_path} - Syntax Error: {e}")
    except Exception as e:
        print(f"✗ {file_path} - Error: {e}")

print("Syntax check completed.")
