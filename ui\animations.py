"""
动画管理器
实现棋子落下、获胜连线、庆祝等动画效果
"""

import tkinter as tk
import math
from typing import Callable, List, Tuple, Optional
from config.settings import (
    ENABLE_ANIMATIONS, ANIMATION_DURATION, PIECE_DROP_FRAMES,
    WIN_LINE_ANIMATION_DURATION, CELEBRATION_ANIMATION_DURATION,
    CURRENT_THEME
)

class AnimationManager:
    """动画管理器类"""

    def __init__(self, canvas: tk.Canvas):
        """
        初始化动画管理器

        Args:
            canvas: 画布对象
        """
        self.canvas = canvas
        self.theme = CURRENT_THEME
        self.running_animations = []  # 正在运行的动画列表

    def animate_piece_drop(self, x: int, y: int, color: str, radius: int,
                          on_complete: Optional[Callable] = None):
        """
        棋子落下动画

        Args:
            x: 目标x坐标
            y: 目标y坐标
            color: 棋子颜色
            radius: 棋子半径
            on_complete: 动画完成回调
        """
        if not ENABLE_ANIMATIONS:
            if on_complete:
                on_complete()
            return

        # 动画参数
        start_y = y - 100  # 从上方100像素开始
        frames = PIECE_DROP_FRAMES
        frame_delay = ANIMATION_DURATION // frames

        # 创建临时棋子
        temp_piece = self.canvas.create_oval(
            x - radius, start_y - radius,
            x + radius, start_y + radius,
            fill=color,
            outline=self.theme['board_lines'],
            width=1,
            tags='temp_animation'
        )

        def animate_frame(frame):
            if frame >= frames:
                # 动画完成，删除临时棋子
                try:
                    self.canvas.delete(temp_piece)
                except tk.TclError:
                    pass  # 临时棋子可能已被删除

                # 确保回调在下一个事件循环中执行，避免时序问题
                if on_complete:
                    self.canvas.after_idle(on_complete)
                return

            # 计算当前位置（使用缓动函数）
            progress = frame / frames
            eased_progress = self._ease_out_bounce(progress)
            current_y = start_y + (y - start_y) * eased_progress

            # 更新棋子位置
            try:
                self.canvas.coords(
                    temp_piece,
                    x - radius, current_y - radius,
                    x + radius, current_y + radius
                )
            except tk.TclError:
                # 如果临时棋子被删除，直接完成动画
                if on_complete:
                    self.canvas.after_idle(on_complete)
                return

            # 安排下一帧
            self.canvas.after(frame_delay, lambda: animate_frame(frame + 1))

        # 开始动画
        animate_frame(0)

    def animate_win_line(self, start_pos: Tuple[int, int], end_pos: Tuple[int, int],
                        on_complete: Optional[Callable] = None):
        """
        获胜连线动画

        Args:
            start_pos: 起始位置 (x, y)
            end_pos: 结束位置 (x, y)
            on_complete: 动画完成回调
        """
        if not ENABLE_ANIMATIONS:
            # 直接绘制完整连线
            self.canvas.create_line(
                start_pos[0], start_pos[1],
                end_pos[0], end_pos[1],
                fill=self.theme['win_line_color'],
                width=4,
                tags='win_line'
            )
            if on_complete:
                on_complete()
            return

        # 动画参数
        frames = 30
        frame_delay = WIN_LINE_ANIMATION_DURATION // frames

        # 创建线条
        line_id = self.canvas.create_line(
            start_pos[0], start_pos[1],
            start_pos[0], start_pos[1],  # 初始长度为0
            fill=self.theme['win_line_color'],
            width=4,
            tags='win_line'
        )

        def animate_frame(frame):
            if frame >= frames:
                # 动画完成
                if on_complete:
                    on_complete()
                return

            # 计算当前终点位置
            progress = frame / frames
            eased_progress = self._ease_out_cubic(progress)

            current_end_x = start_pos[0] + (end_pos[0] - start_pos[0]) * eased_progress
            current_end_y = start_pos[1] + (end_pos[1] - start_pos[1]) * eased_progress

            # 更新线条
            self.canvas.coords(
                line_id,
                start_pos[0], start_pos[1],
                current_end_x, current_end_y
            )

            # 安排下一帧
            self.canvas.after(frame_delay, lambda: animate_frame(frame + 1))

        # 开始动画
        animate_frame(0)

    def animate_celebration(self, center_x: int, center_y: int,
                           on_complete: Optional[Callable] = None):
        """
        庆祝动画（粒子效果）

        Args:
            center_x: 中心x坐标
            center_y: 中心y坐标
            on_complete: 动画完成回调
        """
        if not ENABLE_ANIMATIONS:
            if on_complete:
                on_complete()
            return

        # 创建粒子
        particles = []
        particle_count = 20

        for i in range(particle_count):
            angle = (2 * math.pi * i) / particle_count
            particle = {
                'id': self.canvas.create_oval(
                    center_x - 3, center_y - 3,
                    center_x + 3, center_y + 3,
                    fill=self.theme['accent_color'],
                    outline='',
                    tags='celebration_particle'
                ),
                'angle': angle,
                'speed': 2 + (i % 3),  # 不同的速度
                'life': 1.0
            }
            particles.append(particle)

        frames = 60
        frame_delay = CELEBRATION_ANIMATION_DURATION // frames

        def animate_frame(frame):
            if frame >= frames:
                # 清理粒子
                for particle in particles:
                    self.canvas.delete(particle['id'])
                if on_complete:
                    on_complete()
                return

            # 更新每个粒子
            for particle in particles:
                progress = frame / frames
                distance = particle['speed'] * progress * 50

                new_x = center_x + math.cos(particle['angle']) * distance
                new_y = center_y + math.sin(particle['angle']) * distance

                # 计算透明度（生命值）
                particle['life'] = 1.0 - progress
                alpha = max(0, particle['life'])

                # 更新粒子位置和大小
                size = 3 * alpha
                self.canvas.coords(
                    particle['id'],
                    new_x - size, new_y - size,
                    new_x + size, new_y + size
                )

            # 安排下一帧
            self.canvas.after(frame_delay, lambda: animate_frame(frame + 1))

        # 开始动画
        animate_frame(0)

    def animate_hover_pulse(self, item_id: int, original_color: str):
        """
        悬停脉冲动画

        Args:
            item_id: 要动画的对象ID
            original_color: 原始颜色
        """
        if not ENABLE_ANIMATIONS:
            return

        frames = 20
        frame_delay = 50

        def animate_frame(frame):
            if frame >= frames:
                # 恢复原始颜色
                try:
                    self.canvas.itemconfig(item_id, fill=original_color)
                except tk.TclError:
                    pass  # 对象可能已被删除
                return

            # 计算脉冲效果
            progress = frame / frames
            pulse = (math.sin(progress * math.pi * 4) + 1) / 2  # 0-1之间的脉冲

            # 调整颜色亮度
            try:
                brightness = 0.8 + 0.2 * pulse
                adjusted_color = self._adjust_color_brightness(original_color, brightness)
                self.canvas.itemconfig(item_id, fill=adjusted_color)
            except tk.TclError:
                return  # 对象可能已被删除

            # 安排下一帧
            self.canvas.after(frame_delay, lambda: animate_frame(frame + 1))

        # 开始动画
        animate_frame(0)

    def stop_all_animations(self):
        """停止所有动画"""
        # 删除所有动画相关的临时对象
        self.canvas.delete('temp_animation')
        self.canvas.delete('celebration_particle')

        # 清空动画列表
        self.running_animations.clear()

    def _ease_out_bounce(self, t: float) -> float:
        """
        弹跳缓动函数

        Args:
            t: 进度 (0-1)

        Returns:
            float: 缓动后的值
        """
        if t < 1 / 2.75:
            return 7.5625 * t * t
        elif t < 2 / 2.75:
            t -= 1.5 / 2.75
            return 7.5625 * t * t + 0.75
        elif t < 2.5 / 2.75:
            t -= 2.25 / 2.75
            return 7.5625 * t * t + 0.9375
        else:
            t -= 2.625 / 2.75
            return 7.5625 * t * t + 0.984375

    def _ease_out_cubic(self, t: float) -> float:
        """
        三次缓动函数

        Args:
            t: 进度 (0-1)

        Returns:
            float: 缓动后的值
        """
        return 1 - pow(1 - t, 3)

    def _adjust_color_brightness(self, color: str, factor: float) -> str:
        """
        调整颜色亮度

        Args:
            color: 十六进制颜色值
            factor: 亮度因子 (0-2, 1为原始亮度)

        Returns:
            str: 调整后的颜色值
        """
        if color.startswith('#'):
            color = color[1:]

        try:
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            r = min(255, int(r * factor))
            g = min(255, int(g * factor))
            b = min(255, int(b * factor))

            return f"#{r:02x}{g:02x}{b:02x}"
        except (ValueError, IndexError):
            return color  # 返回原始颜色如果解析失败
