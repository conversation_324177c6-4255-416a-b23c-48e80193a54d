"""
主题管理器
负责主题切换和样式更新
"""

import tkinter as tk
from typing import Dict, Any, Callable, Optional
from config.settings import AVAILABLE_THEMES, DEFAULT_THEME

class ThemeManager:
    """主题管理器类"""
    
    def __init__(self):
        """初始化主题管理器"""
        self.current_theme_name = DEFAULT_THEME
        self.current_theme = AVAILABLE_THEMES[self.current_theme_name]
        self.theme_change_callbacks = []  # 主题变更回调函数列表
    
    def get_current_theme(self) -> Dict[str, Any]:
        """
        获取当前主题
        
        Returns:
            Dict[str, Any]: 当前主题配置
        """
        return self.current_theme
    
    def get_current_theme_name(self) -> str:
        """
        获取当前主题名称
        
        Returns:
            str: 当前主题名称
        """
        return self.current_theme_name
    
    def get_available_themes(self) -> Dict[str, str]:
        """
        获取可用主题列表
        
        Returns:
            Dict[str, str]: 主题名称到显示名称的映射
        """
        return {
            'natural': '清新自然',
            'blue': '现代蓝调'
        }
    
    def set_theme(self, theme_name: str) -> bool:
        """
        设置主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            bool: 是否成功设置
        """
        if theme_name not in AVAILABLE_THEMES:
            return False
        
        old_theme_name = self.current_theme_name
        self.current_theme_name = theme_name
        self.current_theme = AVAILABLE_THEMES[theme_name]
        
        # 通知所有注册的回调函数
        self._notify_theme_change(old_theme_name, theme_name)
        
        return True
    
    def register_theme_change_callback(self, callback: Callable[[str, str], None]):
        """
        注册主题变更回调函数
        
        Args:
            callback: 回调函数，参数为(old_theme_name, new_theme_name)
        """
        if callback not in self.theme_change_callbacks:
            self.theme_change_callbacks.append(callback)
    
    def unregister_theme_change_callback(self, callback: Callable[[str, str], None]):
        """
        取消注册主题变更回调函数
        
        Args:
            callback: 要取消的回调函数
        """
        if callback in self.theme_change_callbacks:
            self.theme_change_callbacks.remove(callback)
    
    def _notify_theme_change(self, old_theme: str, new_theme: str):
        """
        通知主题变更
        
        Args:
            old_theme: 旧主题名称
            new_theme: 新主题名称
        """
        for callback in self.theme_change_callbacks:
            try:
                callback(old_theme, new_theme)
            except Exception as e:
                print(f"主题变更回调函数执行失败: {e}")
    
    def apply_theme_to_widget(self, widget: tk.Widget, widget_type: str = 'frame'):
        """
        将当前主题应用到指定组件
        
        Args:
            widget: 要应用主题的组件
            widget_type: 组件类型 ('frame', 'label', 'button', 'canvas')
        """
        try:
            if widget_type == 'frame':
                widget.configure(bg=self.current_theme['interface_bg'])
            elif widget_type == 'label':
                widget.configure(
                    bg=self.current_theme['interface_bg'],
                    fg=self.current_theme['text_color']
                )
            elif widget_type == 'button':
                widget.configure(
                    bg=self.current_theme['interface_bg'],
                    fg=self.current_theme['text_color'],
                    activebackground=self.current_theme['accent_color']
                )
            elif widget_type == 'canvas':
                widget.configure(bg=self.current_theme['board_bg'])
        except tk.TclError:
            pass  # 忽略不支持的属性
    
    def get_theme_preview_colors(self, theme_name: str) -> Dict[str, str]:
        """
        获取主题预览颜色
        
        Args:
            theme_name: 主题名称
            
        Returns:
            Dict[str, str]: 预览颜色配置
        """
        if theme_name not in AVAILABLE_THEMES:
            return {}
        
        theme = AVAILABLE_THEMES[theme_name]
        return {
            'background': theme['interface_bg'],
            'board': theme['board_bg'],
            'accent': theme['accent_color'],
            'text': theme['text_color']
        }

class ThemePreviewWidget(tk.Frame):
    """主题预览组件"""
    
    def __init__(self, parent: tk.Widget, theme_name: str, theme_manager: ThemeManager):
        """
        初始化主题预览组件
        
        Args:
            parent: 父组件
            theme_name: 主题名称
            theme_manager: 主题管理器
        """
        super().__init__(parent)
        self.theme_name = theme_name
        self.theme_manager = theme_manager
        self.theme = AVAILABLE_THEMES[theme_name]
        
        self.configure(bg=self.theme['interface_bg'], relief='solid', borderwidth=1)
        
        # 创建预览内容
        self._create_preview()
    
    def _create_preview(self):
        """创建预览内容"""
        # 主题名称标签
        name_label = tk.Label(
            self,
            text=self.theme_manager.get_available_themes()[self.theme_name],
            bg=self.theme['interface_bg'],
            fg=self.theme['text_color'],
            font=('Microsoft YaHei', 10, 'bold')
        )
        name_label.pack(pady=5)
        
        # 颜色预览区域
        color_frame = tk.Frame(self, bg=self.theme['interface_bg'])
        color_frame.pack(pady=5)
        
        # 棋盘颜色
        board_color = tk.Frame(
            color_frame,
            bg=self.theme['board_bg'],
            width=20, height=20,
            relief='solid', borderwidth=1
        )
        board_color.pack(side='left', padx=2)
        
        # 强调色
        accent_color = tk.Frame(
            color_frame,
            bg=self.theme['accent_color'],
            width=20, height=20,
            relief='solid', borderwidth=1
        )
        accent_color.pack(side='left', padx=2)
        
        # 黑棋颜色
        black_piece = tk.Frame(
            color_frame,
            bg=self.theme['black_piece'],
            width=20, height=20,
            relief='solid', borderwidth=1
        )
        black_piece.pack(side='left', padx=2)
        
        # 白棋颜色
        white_piece = tk.Frame(
            color_frame,
            bg=self.theme['white_piece'],
            width=20, height=20,
            relief='solid', borderwidth=1
        )
        white_piece.pack(side='left', padx=2)

# 全局主题管理器实例
theme_manager = ThemeManager()
