# 连珠大师 - 五子棋游戏

一个使用Python和Tkinter开发的现代化五子棋游戏，严格按照"连珠大师"设计方案实现。

## 功能特性

### 🎮 游戏功能
- **多种棋盘大小**: 支持11x11、13x13、15x15、17x17、19x19棋盘
- **双人本地对战**: 支持两人轮流对弈
- **人机对战模式**: 与AI对弈，四个难度可选
- **智能AI系统**: 使用Minimax算法和Alpha-Beta剪枝
- **智能胜负判断**: 自动检测五子连珠获胜
- **悔棋功能**: 支持撤销上一步操作
- **游戏设置**: 可调整棋盘大小、游戏模式、AI难度
- **新游戏**: 随时重新开始游戏

### 🎨 界面设计
- **现代简约风格**: 清爽直观的用户界面
- **清新自然主题**: 浅米色棋盘配活力橙强调色
- **流畅交互体验**: 悬停预览、落子动画、获胜连线
- **直观操作**: 点击交叉点落子，简单易用

### 🔧 技术特性
- **模块化架构**: 清晰的代码结构，易于维护
- **响应式UI**: 实时状态更新和视觉反馈
- **错误处理**: 完善的异常处理机制
- **跨平台**: 支持Windows、macOS、Linux

## 项目结构

```
gomoku_master/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖文件
├── README.md              # 项目说明
├── game/                  # 游戏逻辑模块
│   ├── __init__.py
│   ├── board.py           # 棋盘逻辑
│   ├── game_logic.py      # 游戏核心逻辑
│   ├── player.py          # 玩家管理
│   └── ai_player.py       # AI玩家实现
├── ui/                    # 用户界面模块
│   ├── __init__.py
│   ├── main_window.py     # 主窗口
│   ├── game_board_ui.py   # 棋盘UI组件
│   ├── control_panel.py   # 控制面板UI
│   ├── settings_dialog.py # 设置对话框
│   └── styles.py          # 样式和主题配置
└── config/                # 配置模块
    ├── __init__.py
    └── settings.py        # 游戏配置
```

## 安装和运行

### 系统要求
- Python 3.8 或更高版本
- Tkinter (通常随Python一起安装)

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd gomoku_master
   ```

2. **检查Python版本**
   ```bash
   python --version
   ```

3. **运行游戏**
   ```bash
   python main.py
   ```

### 快速开始

1. 运行程序后，点击"设置"选择游戏模式和棋盘大小
2. 点击"新游戏"开始
3. **双人模式**: 黑棋先手，轮流点击棋盘交叉点落子
4. **人机模式**: 玩家执黑棋，AI执白棋，玩家先手
5. 先连成五子者获胜
6. 可使用"悔棋"功能撤销上一步
7. 游戏结束后可开始新游戏

## 游戏规则

- **目标**: 在棋盘上，率先在横、竖、斜任意一个方向上连成五子的一方获胜
- **棋盘大小**: 可选择11x11到19x19的棋盘
- **先手**: 黑棋先行（人机模式下玩家执黑棋）
- **落子**: 点击棋盘交叉点进行落子
- **获胜**: 连成五子即可获胜，系统会自动显示获胜连线
- **悔棋**: 每局游戏可以使用悔棋功能

## AI难度说明

- **简单**: 适合初学者，AI会做出基本的攻防判断
- **中等**: 有一定挑战性，AI会进行2-3步的前瞻计算
- **困难**: 需要仔细思考，AI会进行3-4步的深度搜索
- **专家**: 极具挑战性，AI会进行4-5步的深度分析

## 设计特色

### 视觉设计
- **清新自然主题**: 采用浅米色棋盘背景，营造温馨的对弈环境
- **现代简约风格**: 扁平化设计，界面简洁明了
- **友好的交互反馈**: 悬停预览、落子指示、获胜动画

### 用户体验
- **直观操作**: 鼠标点击即可落子，操作简单
- **实时反馈**: 当前回合、游戏状态一目了然
- **智能提示**: 最后落子位置高亮显示

## 开发说明

### 核心模块

1. **game/board.py**: 棋盘状态管理、落子验证
2. **game/game_logic.py**: 胜负判断、游戏流程控制
3. **ui/game_board_ui.py**: 棋盘绘制、交互处理
4. **ui/control_panel.py**: 控制面板、状态显示

### 扩展功能

项目采用模块化设计，便于扩展以下功能：
- AI对战模式
- 网络对战
- 游戏录像回放
- 多种主题皮肤
- 音效支持

## 许可证

本项目仅供学习和娱乐使用。

## 贡献

欢迎提交Issue和Pull Request来改进游戏！

---

**享受五子棋的乐趣！** 🎯
