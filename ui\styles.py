"""
UI样式和主题配置模块
定义界面的视觉样式和主题
"""

import tkinter as tk
from tkinter import ttk
from config.settings import CURRENT_THEME, FONT_FAMILY, FONT_SIZE_NORMAL, FONT_SIZE_LARGE, FONT_SIZE_TITLE

class StyleManager:
    """样式管理器"""
    
    def __init__(self):
        """初始化样式管理器"""
        self.theme = CURRENT_THEME
        self.fonts = self._create_fonts()
    
    def _create_fonts(self) -> dict:
        """创建字体配置"""
        return {
            'normal': (FONT_FAMILY, FONT_SIZE_NORMAL),
            'large': (FONT_FAMILY, FONT_SIZE_LARGE, 'bold'),
            'title': (FONT_FAMILY, FONT_SIZE_TITLE, 'bold'),
            'button': (FONT_FAMILY, FONT_SIZE_NORMAL, 'bold')
        }
    
    def configure_ttk_styles(self, root: tk.Tk):
        """配置ttk样式"""
        style = ttk.Style(root)
        
        # 配置按钮样式
        style.configure(
            'Game.TButton',
            font=self.fonts['button'],
            padding=(10, 5),
            relief='flat',
            borderwidth=0
        )
        
        # 配置标签样式
        style.configure(
            'Title.TLabel',
            font=self.fonts['title'],
            foreground=self.theme['text_color'],
            background=self.theme['interface_bg']
        )
        
        style.configure(
            'Info.TLabel',
            font=self.fonts['normal'],
            foreground=self.theme['text_color'],
            background=self.theme['interface_bg']
        )
        
        style.configure(
            'Status.TLabel',
            font=self.fonts['large'],
            foreground=self.theme['accent_color'],
            background=self.theme['interface_bg']
        )
    
    def get_button_style(self, button_type: str = 'normal') -> dict:
        """
        获取按钮样式配置
        
        Args:
            button_type: 按钮类型 ('normal', 'primary', 'danger')
            
        Returns:
            dict: 按钮样式配置
        """
        base_style = {
            'font': self.fonts['button'],
            'relief': 'flat',
            'borderwidth': 0,
            'cursor': 'hand2',
            'padx': 15,
            'pady': 8
        }
        
        if button_type == 'primary':
            base_style.update({
                'bg': self.theme['accent_color'],
                'fg': 'white',
                'activebackground': self._darken_color(self.theme['accent_color']),
                'activeforeground': 'white'
            })
        elif button_type == 'danger':
            base_style.update({
                'bg': '#F44336',
                'fg': 'white',
                'activebackground': '#D32F2F',
                'activeforeground': 'white'
            })
        else:  # normal
            base_style.update({
                'bg': '#E0E0E0',
                'fg': self.theme['text_color'],
                'activebackground': '#D0D0D0',
                'activeforeground': self.theme['text_color']
            })
        
        return base_style
    
    def get_label_style(self, label_type: str = 'normal') -> dict:
        """
        获取标签样式配置
        
        Args:
            label_type: 标签类型 ('normal', 'title', 'status')
            
        Returns:
            dict: 标签样式配置
        """
        base_style = {
            'bg': self.theme['interface_bg'],
            'fg': self.theme['text_color']
        }
        
        if label_type == 'title':
            base_style.update({
                'font': self.fonts['title']
            })
        elif label_type == 'status':
            base_style.update({
                'font': self.fonts['large'],
                'fg': self.theme['accent_color']
            })
        else:  # normal
            base_style.update({
                'font': self.fonts['normal']
            })
        
        return base_style
    
    def get_frame_style(self) -> dict:
        """获取框架样式配置"""
        return {
            'bg': self.theme['interface_bg'],
            'relief': 'flat',
            'borderwidth': 0
        }
    
    def _darken_color(self, color: str, factor: float = 0.8) -> str:
        """
        使颜色变暗
        
        Args:
            color: 十六进制颜色值
            factor: 变暗因子 (0-1)
            
        Returns:
            str: 变暗后的颜色值
        """
        # 简单的颜色变暗实现
        if color.startswith('#'):
            color = color[1:]
        
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def create_rounded_button(self, parent, text: str, command=None, button_type: str = 'normal') -> tk.Button:
        """
        创建圆角按钮
        
        Args:
            parent: 父组件
            text: 按钮文本
            command: 点击回调函数
            button_type: 按钮类型
            
        Returns:
            tk.Button: 创建的按钮
        """
        style = self.get_button_style(button_type)
        button = tk.Button(parent, text=text, command=command, **style)
        
        # 添加悬停效果
        def on_enter(event):
            if button_type == 'primary':
                button.config(bg=self._darken_color(self.theme['accent_color']))
            else:
                button.config(bg='#D0D0D0')
        
        def on_leave(event):
            button.config(bg=style['bg'])
        
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)
        
        return button

# 全局样式管理器实例
style_manager = StyleManager()
