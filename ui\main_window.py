"""
主窗口UI组件
整合棋盘和控制面板，管理整体界面布局
"""

import tkinter as tk
from tkinter import messagebox
from ui.game_board_ui import GameBoardUI
from ui.control_panel import ControlPanel
from ui.settings_dialog import show_settings_dialog
from ui.styles import style_manager
from game.game_logic import GameLogic, GameState
from game.player import PieceColor
from config.settings import (
    WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_TITLE, CURRENT_THEME,
    BOARD_SIZE, DEFAULT_GAME_MODE, DEFAULT_AI_DIFFICULTY, DEFAULT_THEME
)
from ui.theme_manager import theme_manager

class MainWindow:
    """主窗口类"""

    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.theme = CURRENT_THEME

        # 游戏设置
        self.game_settings = {
            'board_size': BOARD_SIZE,
            'game_mode': DEFAULT_GAME_MODE,
            'ai_difficulty': DEFAULT_AI_DIFFICULTY,
            'theme': DEFAULT_THEME
        }

        self.game_logic = GameLogic(self.game_settings['board_size'])
        self.ai_thinking = False  # AI是否正在思考

        # 配置窗口
        self._setup_window()

        # 创建UI组件
        self._create_ui_components()

        # 绑定事件
        self._bind_events()

        # 初始化游戏状态
        self._update_ui_state()

    def _setup_window(self):
        """配置主窗口"""
        self.root.title(WINDOW_TITLE)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.configure(bg=self.theme['interface_bg'])
        self.root.resizable(False, False)

        # 居中显示窗口
        self._center_window()

        # 配置样式
        style_manager.configure_ttk_styles(self.root)

    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _create_ui_components(self):
        """创建UI组件"""
        # 创建主容器
        self.main_container = tk.Frame(self.root, bg=self.theme['interface_bg'])
        self.main_container.pack(fill='both', expand=True)

        # 创建左侧棋盘区域
        self.board_frame = tk.Frame(
            self.main_container,
            bg=self.theme['interface_bg'],
            relief='flat',
            borderwidth=0
        )
        self.board_frame.pack(side='left', fill='both', expand=True, padx=(20, 10), pady=20)

        # 创建棋盘UI
        self.board_ui = GameBoardUI(self.board_frame, self._on_board_click)
        self.board_ui.get_widget().pack()

        # 创建右侧控制面板区域
        self.control_frame = tk.Frame(
            self.main_container,
            bg=self.theme['interface_bg'],
            relief='flat',
            borderwidth=0,
            width=250
        )
        self.control_frame.pack(side='right', fill='y', padx=(10, 20), pady=20)
        self.control_frame.pack_propagate(False)  # 固定宽度

        # 创建控制面板UI
        self.control_panel = ControlPanel(self.control_frame)

    def _bind_events(self):
        """绑定事件"""
        # 绑定控制面板事件
        self.control_panel.on_new_game = self._on_new_game
        self.control_panel.on_undo = self._on_undo
        self.control_panel.on_settings = self._on_settings
        self.control_panel.on_exit = self._on_exit

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)

    def _on_board_click(self, row: int, col: int):
        """
        处理棋盘点击事件

        Args:
            row: 点击的行坐标
            col: 点击的列坐标
        """
        if self.game_logic.state != GameState.PLAYING or self.ai_thinking:
            return

        # 在AI模式下，如果轮到AI则忽略点击
        if self.game_logic.is_ai_turn():
            return

        # 尝试落子
        if self.game_logic.make_move(row, col):
            # 更新棋盘UI
            current_player = self.game_logic.players.get_opponent()  # 获取刚才落子的玩家
            self.board_ui.place_piece(row, col, current_player.piece_color)

            # 更新UI状态
            self._update_ui_state()

            # 检查游戏是否结束
            if self.game_logic.is_game_over():
                self._handle_game_over()
            else:
                # 如果是AI模式且轮到AI，让AI落子
                if self.game_logic.is_ai_turn():
                    self.root.after(500, self._ai_make_move)  # 延迟500ms让AI落子

    def _on_new_game(self):
        """处理新游戏事件"""
        # 如果游戏正在进行，询问确认
        if (self.game_logic.state == GameState.PLAYING and
            self.game_logic.board.get_move_count() > 0):
            result = messagebox.askyesno(
                "确认",
                "当前游戏正在进行中，确定要开始新游戏吗？",
                parent=self.root
            )
            if not result:
                return

        # 应用当前设置
        self._apply_game_settings()

        # 开始新游戏
        self.game_logic.start_new_game()
        self.board_ui.clear_board()
        self._update_ui_state()

        # 如果是AI模式且AI先手，让AI落子
        if self.game_logic.is_ai_turn():
            self.root.after(1000, self._ai_make_move)

    def _on_undo(self):
        """处理悔棋事件"""
        if not self.game_logic.can_undo():
            return

        # 获取最后一步的位置
        last_move = self.game_logic.get_last_move()

        # 执行悔棋
        if self.game_logic.undo_move() and last_move:
            # 更新棋盘UI
            self.board_ui.remove_piece(last_move[0], last_move[1])
            self._update_ui_state()

    def _on_settings(self):
        """处理设置事件"""
        # 显示设置对话框
        new_settings = show_settings_dialog(self.root, self.game_settings)

        if new_settings:
            # 检查是否需要重新创建棋盘
            board_size_changed = new_settings['board_size'] != self.game_settings['board_size']
            theme_changed = new_settings.get('theme') != self.game_settings.get('theme')

            # 更新设置
            self.game_settings.update(new_settings)

            # 应用主题变更
            if theme_changed:
                theme_manager.set_theme(new_settings['theme'])
                self._apply_theme_change()

            if board_size_changed:
                # 如果棋盘大小改变，需要重新创建棋盘UI
                self._recreate_board_ui()

            # 应用设置
            self._apply_game_settings()

            # 如果游戏正在进行，询问是否重新开始
            if (self.game_logic.state == GameState.PLAYING and
                self.game_logic.board.get_move_count() > 0):
                result = messagebox.askyesno(
                    "设置已更改",
                    "设置已更改，是否重新开始游戏？",
                    parent=self.root
                )
                if result:
                    self._on_new_game()

    def _on_exit(self):
        """处理退出事件"""
        self._on_window_close()

    def _on_window_close(self):
        """处理窗口关闭事件"""
        if (self.game_logic.state == GameState.PLAYING and
            self.game_logic.board.get_move_count() > 0):
            result = messagebox.askyesno(
                "确认退出",
                "游戏正在进行中，确定要退出吗？",
                parent=self.root
            )
            if not result:
                return

        self.root.quit()
        self.root.destroy()

    def _apply_game_settings(self):
        """应用游戏设置"""
        # 设置棋盘大小
        self.game_logic.set_board_size(self.game_settings['board_size'])

        # 设置游戏模式
        self.game_logic.set_game_mode(
            self.game_settings['game_mode'],
            self.game_settings['ai_difficulty']
        )

    def _recreate_board_ui(self):
        """重新创建棋盘UI（当棋盘大小改变时）"""
        # 销毁旧的棋盘UI
        self.board_ui.get_widget().destroy()

        # 创建新的棋盘UI
        self.board_ui = GameBoardUI(self.board_frame, self._on_board_click)
        self.board_ui.get_widget().pack()

    def _ai_make_move(self):
        """AI落子"""
        if (self.game_logic.state != GameState.PLAYING or
            not self.game_logic.is_ai_turn() or
            self.ai_thinking):
            return

        self.ai_thinking = True
        self.control_panel.update_status("AI正在思考...")

        # 获取AI的落子位置
        ai_move = self.game_logic.get_ai_move()

        if ai_move:
            row, col = ai_move

            # AI落子
            if self.game_logic.make_move(row, col):
                # 更新棋盘UI
                current_player = self.game_logic.players.get_opponent()
                self.board_ui.place_piece(row, col, current_player.piece_color)

                # 检查游戏是否结束
                if self.game_logic.is_game_over():
                    self._handle_game_over()

        self.ai_thinking = False
        self._update_ui_state()

    def _update_ui_state(self):
        """更新UI状态"""
        # 更新当前玩家显示
        if self.game_logic.state == GameState.PLAYING:
            current_player = self.game_logic.players.get_current_player()
            self.control_panel.update_current_player(current_player.piece_color)
            self.board_ui.set_current_player(current_player.piece_color)

        # 更新游戏状态显示
        status_text = self.game_logic.get_game_status_text()
        self.control_panel.update_status(status_text)

        # 更新步数显示
        move_count = self.game_logic.board.get_move_count()
        self.control_panel.update_move_count(move_count)

        # 更新悔棋按钮状态
        can_undo = self.game_logic.can_undo()
        self.control_panel.set_undo_enabled(can_undo)

    def _handle_game_over(self):
        """处理游戏结束"""
        if self.game_logic.win_info:
            # 绘制获胜连线
            self.board_ui.draw_win_line(self.game_logic.win_info.win_line)

            # 显示获胜消息
            winner_name = "黑棋" if self.game_logic.win_info.winner == PieceColor.BLACK else "白棋"
            messagebox.showinfo(
                "游戏结束",
                f"恭喜！{winner_name}获胜！",
                parent=self.root
            )
        elif self.game_logic.state == GameState.DRAW:
            messagebox.showinfo(
                "游戏结束",
                "平局！棋盘已满。",
                parent=self.root
            )

    def run(self):
        """运行主窗口"""
        self.root.mainloop()

def create_and_run_game():
    """创建并运行游戏"""
    app = MainWindow()
    app.run()
