#!/usr/bin/env python3
"""
简化版游戏测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    try:
        print("测试基本导入...")
        
        # 测试标准库
        import tkinter as tk
        print("✓ Tkinter导入成功")
        
        # 测试配置
        from config.settings import BOARD_SIZE, CURRENT_THEME
        print(f"✓ 配置导入成功 - 棋盘大小: {BOARD_SIZE}")
        
        # 测试游戏逻辑
        from game.player import PieceColor
        from game.board import GameBoard
        from game.game_logic import GameLogic
        print("✓ 游戏逻辑模块导入成功")
        
        # 测试AI
        from game.ai_player import AIPlayer
        print("✓ AI模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_game_logic():
    """测试游戏逻辑"""
    try:
        print("\n测试游戏逻辑...")
        
        from game.game_logic import GameLogic
        from game.player import PieceColor
        
        # 创建游戏
        game = GameLogic()
        print(f"✓ 游戏创建成功 - 状态: {game.state}")
        
        # 开始游戏
        game.start_new_game()
        print("✓ 新游戏开始成功")
        
        # 测试落子
        success = game.make_move(7, 7)
        print(f"✓ 落子测试: {success}")
        
        # 测试AI模式
        game.set_game_mode('pve', 'easy')
        print("✓ AI模式设置成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 游戏逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_creation():
    """测试UI创建"""
    try:
        print("\n测试UI创建...")
        
        import tkinter as tk
        from ui.styles import style_manager
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        print("✓ 根窗口创建成功")
        
        # 测试样式管理器
        style = style_manager.get_button_style('primary')
        print("✓ 样式管理器工作正常")
        
        # 销毁窗口
        root.destroy()
        print("✓ UI测试完成")
        
        return True
        
    except Exception as e:
        print(f"✗ UI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始五子棋游戏测试...")
    print("=" * 40)
    
    # 测试基本导入
    if not test_basic_imports():
        print("基本导入测试失败，退出")
        return False
    
    # 测试游戏逻辑
    if not test_game_logic():
        print("游戏逻辑测试失败，退出")
        return False
    
    # 测试UI创建
    if not test_ui_creation():
        print("UI测试失败，退出")
        return False
    
    print("\n" + "=" * 40)
    print("所有测试通过！游戏应该可以正常运行。")
    
    # 询问是否启动完整游戏
    try:
        choice = input("\n是否启动完整游戏？(y/n): ").lower().strip()
        if choice == 'y':
            print("启动完整游戏...")
            from ui.main_window import create_and_run_game
            create_and_run_game()
    except KeyboardInterrupt:
        print("\n用户取消")
    except Exception as e:
        print(f"启动游戏失败: {e}")
    
    return True

if __name__ == "__main__":
    main()
